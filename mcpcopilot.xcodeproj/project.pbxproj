// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		596037C32E26AA39002B4A4B /* RiveRuntime in Frameworks */ = {isa = PBXBuildFile; productRef = 596037C22E26AA39002B4A4B /* RiveRuntime */; };
		596037C62E26AA73002B4A4B /* SwiftGodotKit in Frameworks */ = {isa = PBXBuildFile; productRef = 596037C52E26AA73002B4A4B /* SwiftGodotKit */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		59D971CD2E1E8BFF00946D72 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 59D971B42E1E8BFE00946D72 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 59D971BB2E1E8BFE00946D72;
			remoteInfo = mcpcopilot;
		};
		59D971D72E1E8BFF00946D72 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 59D971B42E1E8BFE00946D72 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 59D971BB2E1E8BFE00946D72;
			remoteInfo = mcpcopilot;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		59D971BC2E1E8BFE00946D72 /* mcpcopilot.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = mcpcopilot.app; sourceTree = BUILT_PRODUCTS_DIR; };
		59D971CC2E1E8BFF00946D72 /* mcpcopilotTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = mcpcopilotTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		59D971D62E1E8BFF00946D72 /* mcpcopilotUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = mcpcopilotUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		59D971BE2E1E8BFE00946D72 /* mcpcopilot */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = mcpcopilot;
			sourceTree = "<group>";
		};
		59D971CF2E1E8BFF00946D72 /* mcpcopilotTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = mcpcopilotTests;
			sourceTree = "<group>";
		};
		59D971D92E1E8BFF00946D72 /* mcpcopilotUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = mcpcopilotUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		59D971B92E1E8BFE00946D72 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				596037C32E26AA39002B4A4B /* RiveRuntime in Frameworks */,
				596037C62E26AA73002B4A4B /* SwiftGodotKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971C92E1E8BFF00946D72 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971D32E1E8BFF00946D72 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		59D971B32E1E8BFE00946D72 = {
			isa = PBXGroup;
			children = (
				59D971BE2E1E8BFE00946D72 /* mcpcopilot */,
				59D971CF2E1E8BFF00946D72 /* mcpcopilotTests */,
				59D971D92E1E8BFF00946D72 /* mcpcopilotUITests */,
				59D971BD2E1E8BFE00946D72 /* Products */,
			);
			sourceTree = "<group>";
		};
		59D971BD2E1E8BFE00946D72 /* Products */ = {
			isa = PBXGroup;
			children = (
				59D971BC2E1E8BFE00946D72 /* mcpcopilot.app */,
				59D971CC2E1E8BFF00946D72 /* mcpcopilotTests.xctest */,
				59D971D62E1E8BFF00946D72 /* mcpcopilotUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		59D971BB2E1E8BFE00946D72 /* mcpcopilot */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 59D971E02E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilot" */;
			buildPhases = (
				59D971B82E1E8BFE00946D72 /* Sources */,
				59D971B92E1E8BFE00946D72 /* Frameworks */,
				59D971BA2E1E8BFE00946D72 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				59D971BE2E1E8BFE00946D72 /* mcpcopilot */,
			);
			name = mcpcopilot;
			packageProductDependencies = (
				596037C22E26AA39002B4A4B /* RiveRuntime */,
				596037C52E26AA73002B4A4B /* SwiftGodotKit */,
			);
			productName = mcpcopilot;
			productReference = 59D971BC2E1E8BFE00946D72 /* mcpcopilot.app */;
			productType = "com.apple.product-type.application";
		};
		59D971CB2E1E8BFF00946D72 /* mcpcopilotTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 59D971E32E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilotTests" */;
			buildPhases = (
				59D971C82E1E8BFF00946D72 /* Sources */,
				59D971C92E1E8BFF00946D72 /* Frameworks */,
				59D971CA2E1E8BFF00946D72 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				59D971CE2E1E8BFF00946D72 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				59D971CF2E1E8BFF00946D72 /* mcpcopilotTests */,
			);
			name = mcpcopilotTests;
			packageProductDependencies = (
			);
			productName = mcpcopilotTests;
			productReference = 59D971CC2E1E8BFF00946D72 /* mcpcopilotTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		59D971D52E1E8BFF00946D72 /* mcpcopilotUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 59D971E62E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilotUITests" */;
			buildPhases = (
				59D971D22E1E8BFF00946D72 /* Sources */,
				59D971D32E1E8BFF00946D72 /* Frameworks */,
				59D971D42E1E8BFF00946D72 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				59D971D82E1E8BFF00946D72 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				59D971D92E1E8BFF00946D72 /* mcpcopilotUITests */,
			);
			name = mcpcopilotUITests;
			packageProductDependencies = (
			);
			productName = mcpcopilotUITests;
			productReference = 59D971D62E1E8BFF00946D72 /* mcpcopilotUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		59D971B42E1E8BFE00946D72 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					59D971BB2E1E8BFE00946D72 = {
						CreatedOnToolsVersion = 16.4;
					};
					59D971CB2E1E8BFF00946D72 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 59D971BB2E1E8BFE00946D72;
					};
					59D971D52E1E8BFF00946D72 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 59D971BB2E1E8BFE00946D72;
					};
				};
			};
			buildConfigurationList = 59D971B72E1E8BFE00946D72 /* Build configuration list for PBXProject "mcpcopilot" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				de,
				fr,
				ja,
			);
			mainGroup = 59D971B32E1E8BFE00946D72;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				596037C12E26AA39002B4A4B /* XCRemoteSwiftPackageReference "rive-ios" */,
				596037C42E26AA73002B4A4B /* XCRemoteSwiftPackageReference "SwiftGodotKit" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 59D971BD2E1E8BFE00946D72 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				59D971BB2E1E8BFE00946D72 /* mcpcopilot */,
				59D971CB2E1E8BFF00946D72 /* mcpcopilotTests */,
				59D971D52E1E8BFF00946D72 /* mcpcopilotUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		59D971BA2E1E8BFE00946D72 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971CA2E1E8BFF00946D72 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971D42E1E8BFF00946D72 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		59D971B82E1E8BFE00946D72 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971C82E1E8BFF00946D72 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D971D22E1E8BFF00946D72 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		59D971CE2E1E8BFF00946D72 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 59D971BB2E1E8BFE00946D72 /* mcpcopilot */;
			targetProxy = 59D971CD2E1E8BFF00946D72 /* PBXContainerItemProxy */;
		};
		59D971D82E1E8BFF00946D72 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 59D971BB2E1E8BFE00946D72 /* mcpcopilot */;
			targetProxy = 59D971D72E1E8BFF00946D72 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		59D971DE2E1E8BFF00946D72 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		59D971DF2E1E8BFF00946D72 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		59D971E12E1E8BFF00946D72 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = mcpcopilot/mcpcopilot.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = McpCopilot;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		59D971E22E1E8BFF00946D72 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = mcpcopilot/mcpcopilot.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = McpCopilot;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		59D971E42E1E8BFF00946D72 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilotTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/mcpcopilot.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/mcpcopilot";
			};
			name = Debug;
		};
		59D971E52E1E8BFF00946D72 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilotTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/mcpcopilot.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/mcpcopilot";
			};
			name = Release;
		};
		59D971E72E1E8BFF00946D72 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilotUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = mcpcopilot;
			};
			name = Debug;
		};
		59D971E82E1E8BFF00946D72 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.mcpcopilotUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = mcpcopilot;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		59D971B72E1E8BFE00946D72 /* Build configuration list for PBXProject "mcpcopilot" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				59D971DE2E1E8BFF00946D72 /* Debug */,
				59D971DF2E1E8BFF00946D72 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		59D971E02E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilot" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				59D971E12E1E8BFF00946D72 /* Debug */,
				59D971E22E1E8BFF00946D72 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		59D971E32E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilotTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				59D971E42E1E8BFF00946D72 /* Debug */,
				59D971E52E1E8BFF00946D72 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		59D971E62E1E8BFF00946D72 /* Build configuration list for PBXNativeTarget "mcpcopilotUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				59D971E72E1E8BFF00946D72 /* Debug */,
				59D971E82E1E8BFF00946D72 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		596037C12E26AA39002B4A4B /* XCRemoteSwiftPackageReference "rive-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/rive-app/rive-ios.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.10.0;
			};
		};
		596037C42E26AA73002B4A4B /* XCRemoteSwiftPackageReference "SwiftGodotKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/migueldeicaza/SwiftGodotKit.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		596037C22E26AA39002B4A4B /* RiveRuntime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 596037C12E26AA39002B4A4B /* XCRemoteSwiftPackageReference "rive-ios" */;
			productName = RiveRuntime;
		};
		596037C52E26AA73002B4A4B /* SwiftGodotKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 596037C42E26AA73002B4A4B /* XCRemoteSwiftPackageReference "SwiftGodotKit" */;
			productName = SwiftGodotKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 59D971B42E1E8BFE00946D72 /* Project object */;
}
