{"originHash": "d242f5d7cc2b9227dbc89fdb81734164b0e40f793f4654699644e1c9e6ded838", "pins": [{"identity": "rive-ios", "kind": "remoteSourceControl", "location": "https://github.com/rive-app/rive-ios.git", "state": {"revision": "d8e40f654a4cd26bc8aee4a291ec7c8eb13fc8ce", "version": "6.10.0"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser", "state": {"revision": "309a47b2b1d9b5e991f36961c983ecec72275be3", "version": "1.6.1"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "0687f71944021d616d34d922343dcef086855920", "version": "600.0.1"}}, {"identity": "swiftgodot", "kind": "remoteSourceControl", "location": "https://github.com/migueldeicaza/SwiftGodot", "state": {"revision": "20d2d7a35d2ad392ec556219ea004da14ab7c1d4"}}, {"identity": "swiftgodotkit", "kind": "remoteSourceControl", "location": "https://github.com/migueldeicaza/SwiftGodotKit.git", "state": {"branch": "main", "revision": "8f426d5b224cc9d45639f53152235d4e8137c847"}}], "version": 3}