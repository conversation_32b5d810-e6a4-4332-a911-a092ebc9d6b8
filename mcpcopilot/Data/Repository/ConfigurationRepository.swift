//
//  ConfigurationRepository.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class ConfigurationRepository: ConfigurationRepositoryProtocol {
    
    // MARK: - Properties
    @Published private(set) var appConfiguration: AppConfiguration = .default
    
    private let storageService: StorageServiceProtocol
    private let storageKey = "app_configuration"
    
    // MARK: - Initialization
    init(storageService: StorageServiceProtocol) {
        self.storageService = storageService
        
        Task {
            try? await loadConfiguration()
        }
    }
    
    // MARK: - Configuration Management
    func loadConfiguration() async throws {
        do {
            if let loadedConfig: AppConfiguration = try await storageService.load(AppConfiguration.self, from: storageKey) {
                appConfiguration = loadedConfig
            } else {
                appConfiguration = .default
                try await saveConfiguration(appConfiguration)
            }
        } catch {
            appConfiguration = .default
            try await saveConfiguration(appConfiguration)
            throw error
        }
    }
    
    func saveConfiguration(_ config: AppConfiguration) async throws {
        appConfiguration = config
        try await storageService.save(config, to: storageKey)
    }
    
    func resetToDefault() async throws {
        appConfiguration = .default
        try await storageService.save(appConfiguration, to: storageKey)
    }
}

// MARK: - Configuration Helpers
extension ConfigurationRepository {
    
    func updateDefaultPort(_ port: Int) async throws {
        var config = appConfiguration
        config.defaultPort = port
        try await saveConfiguration(config)
    }
    
    func updateAutoStart(_ autoStart: Bool) async throws {
        var config = appConfiguration
        config.autoStart = autoStart
        try await saveConfiguration(config)
    }
    
    func updateLogLevel(_ logLevel: LogLevel) async throws {
        var config = appConfiguration
        config.logLevel = logLevel
        try await saveConfiguration(config)
    }
    
    func updateMaxRestartAttempts(_ attempts: Int) async throws {
        var config = appConfiguration
        config.maxRestartAttempts = attempts
        try await saveConfiguration(config)
    }
    
    func updateMonitoringInterval(_ interval: TimeInterval) async throws {
        var config = appConfiguration
        config.monitoringInterval = interval
        try await saveConfiguration(config)
    }
    
    func updateSystemTrayEnabled(_ enabled: Bool) async throws {
        var config = appConfiguration
        config.enableSystemTray = enabled
        try await saveConfiguration(config)
    }
    
    func updateLanguage(_ language: String) async throws {
        var config = appConfiguration
        config.language = language
        try await saveConfiguration(config)
    }
}
