//
//  DataModels.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import SwiftData

// MARK: - MCP Scene Model
@Model
final class MCPScene {
    var id: UUID
    var name: String
    var port: Int
    var isActive: Bool
    var environment: [String: String]
    var createdAt: Date
    var updatedAt: Date
    
    @Relationship(deleteRule: .cascade, inverse: \MCPServer.scene)
    var servers: [MCPServer] = []
    
    init(
        id: UUID = UUID(),
        name: String,
        port: Int,
        isActive: Bool = false,
        environment: [String: String] = [:],
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.port = port
        self.isActive = isActive
        self.environment = environment
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - MCP Server Model
@Model
final class MCPServer {
    var id: UUID
    var name: String
    var namespace: String
    var type: MCPServerType
    var command: String
    var arguments: [String]
    var isRunning: Bool
    var pid: Int32?
    var startTime: Date?
    var restartCount: Int
    var createdAt: Date
    var updatedAt: Date
    
    var scene: MCPScene?
    
    init(
        id: UUID = UUID(),
        name: String,
        namespace: String,
        type: MCPServerType,
        command: String,
        arguments: [String] = [],
        isRunning: Bool = false,
        pid: Int32? = nil,
        startTime: Date? = nil,
        restartCount: Int = 0,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.namespace = namespace
        self.type = type
        self.command = command
        self.arguments = arguments
        self.isRunning = isRunning
        self.pid = pid
        self.startTime = startTime
        self.restartCount = restartCount
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - MCP Server Type
enum MCPServerType: String, CaseIterable, Codable {
    case stdio = "stdio"
    case http = "http"
    
    var displayName: String {
        switch self {
        case .stdio:
            return "Standard I/O"
        case .http:
            return "HTTP"
        }
    }
}

// MARK: - Process Status
enum ProcessStatus: String, CaseIterable {
    case stopped = "stopped"
    case starting = "starting"
    case running = "running"
    case stopping = "stopping"
    case error = "error"
    
    var displayName: String {
        switch self {
        case .stopped:
            return "已停止"
        case .starting:
            return "启动中"
        case .running:
            return "运行中"
        case .stopping:
            return "停止中"
        case .error:
            return "错误"
        }
    }
}

// MARK: - System Resource Info
struct SystemResourceInfo {
    let cpuUsage: Double
    let memoryUsage: Double
    let totalMemory: UInt64
    let usedMemory: UInt64
    let timestamp: Date
    
    init(
        cpuUsage: Double = 0.0,
        memoryUsage: Double = 0.0,
        totalMemory: UInt64 = 0,
        usedMemory: UInt64 = 0,
        timestamp: Date = Date()
    ) {
        self.cpuUsage = cpuUsage
        self.memoryUsage = memoryUsage
        self.totalMemory = totalMemory
        self.usedMemory = usedMemory
        self.timestamp = timestamp
    }
}

// MARK: - Process Resource Info
struct ProcessResourceInfo {
    let pid: Int32
    let cpuUsage: Double
    let memoryUsage: UInt64
    let status: ProcessStatus
    let uptime: TimeInterval
    let timestamp: Date
    
    init(
        pid: Int32,
        cpuUsage: Double = 0.0,
        memoryUsage: UInt64 = 0,
        status: ProcessStatus = .stopped,
        uptime: TimeInterval = 0,
        timestamp: Date = Date()
    ) {
        self.pid = pid
        self.cpuUsage = cpuUsage
        self.memoryUsage = memoryUsage
        self.status = status
        self.uptime = uptime
        self.timestamp = timestamp
    }
}

// MARK: - Log Entry
struct LogEntry: Identifiable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let message: String
    let source: String?
    
    init(
        timestamp: Date = Date(),
        level: LogLevel,
        message: String,
        source: String? = nil
    ) {
        self.timestamp = timestamp
        self.level = level
        self.message = message
        self.source = source
    }
}

// MARK: - Log Level
enum LogLevel: String, CaseIterable, Codable {
    case debug = "debug"
    case info = "info"
    case warning = "warning"
    case error = "error"
    
    var displayName: String {
        switch self {
        case .debug:
            return "调试"
        case .info:
            return "信息"
        case .warning:
            return "警告"
        case .error:
            return "错误"
        }
    }
}
