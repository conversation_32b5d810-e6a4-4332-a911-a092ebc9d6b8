//
//  LocalizationDemoView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct LocalizationDemoView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var localizationService: LocalizationService
    
    @State private var selectedLanguage: String = "zh-<PERSON>"
    @State private var testString = "app.title"
    @State private var customArguments = ["MCP Copilot", "1.0.0"]
    
    // MARK: - Initialization
    init() {
        self._localizationService = StateObject(wrappedValue: DIContainer.shared.localizationService as! LocalizationService)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            // 标题
            Text("本地化演示")
                .pageTitle()
            
            // 语言选择
            languageSelectionSection
            
            // 基本本地化演示
            basicLocalizationSection
            
            // 带参数的本地化演示
            parametrizedLocalizationSection
            
            // 实时测试
            liveTestSection
            
            Spacer()
        }
        .contentPadding()
        .navigationTitle("本地化演示")
        .onAppear {
            selectedLanguage = localizationService.currentLanguage
        }
    }
    
    // MARK: - Language Selection Section
    private var languageSelectionSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("语言选择")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("当前语言: \(localizationService.currentLanguage)")
                    .bodyText()
                
                Picker("选择语言", selection: $selectedLanguage) {
                    ForEach(localizationService.availableLanguages, id: \.code) { language in
                        Text("\(language.nativeName) (\(language.name))")
                            .tag(language.code)
                    }
                }
                .pickerStyle(.segmented)
                .onChange(of: selectedLanguage) { _, newValue in
                    localizationService.setLanguage(newValue)
                }
                
                Text("更改语言后，应用中的所有文本都会立即更新。")
                    .captionText()
                    .foregroundColor(.textSecondary)
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Basic Localization Section
    private var basicLocalizationSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("基本本地化")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                localizationExample("app.title", "应用标题")
                localizationExample("scene.title", "场景")
                localizationExample("settings.title", "设置")
                localizationExample("monitor.title", "监控")
                localizationExample("log.title", "日志")
                localizationExample("welcome.title", "欢迎标题")
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Parametrized Localization Section
    private var parametrizedLocalizationSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("带参数的本地化")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("演示如何在本地化字符串中使用参数：")
                    .bodyText()
                
                Divider()
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("示例 1: 确认删除")
                        .labelText()
                    
                    Text("Key: confirm.delete_scene")
                        .codeText()
                        .foregroundColor(.textSecondary)
                    
                    Text(localizationService.localizedString(for: "confirm.delete_scene", arguments: "开发环境"))
                        .bodyText()
                        .foregroundColor(.primaryBlue)
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("示例 2: 错误信息")
                        .labelText()
                    
                    Text("Key: error.load_scenes")
                        .codeText()
                        .foregroundColor(.textSecondary)
                    
                    Text(localizationService.localizedString(for: "error.load_scenes"))
                        .bodyText()
                        .foregroundColor(.statusError)
                }
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Live Test Section
    private var liveTestSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("实时测试")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("输入本地化键来测试：")
                    .bodyText()
                
                TextField("本地化键 (例如: app.title)", text: $testString)
                    .textFieldStyle(.roundedBorder)
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("结果:")
                        .labelText()
                    
                    Text(localizationService.localizedString(for: testString, defaultValue: "未找到对应的本地化字符串"))
                        .bodyText()
                        .foregroundColor(.primaryBlue)
                        .padding(.horizontal, Spacing.sm)
                        .padding(.vertical, Spacing.xs)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.backgroundSecondary)
                        )
                }
                
                Text("提示: 尝试输入 'scene.title', 'settings.general', 'error.invalid_port' 等键。")
                    .captionText()
                    .foregroundColor(.textSecondary)
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Helper Views
    private func localizationExample(_ key: String, _ description: String) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(description)
                    .labelText()
                
                Text("Key: \(key)")
                    .codeText()
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            Text(localizationService.localizedString(for: key, defaultValue: key))
                .bodyText()
                .foregroundColor(.primaryBlue)
                .multilineTextAlignment(.trailing)
        }
    }
}

// MARK: - Localization Status View
struct LocalizationStatusView: View {
    @Environment(\.diContainer) private var diContainer
    @StateObject private var localizationService: LocalizationService
    
    init() {
        self._localizationService = StateObject(wrappedValue: DIContainer.shared.localizationService as! LocalizationService)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.primaryBlue)
                
                Text("本地化状态")
                    .labelText()
                
                Spacer()
                
                Text(localizationService.currentLanguage)
                    .captionText()
                    .padding(.horizontal, Spacing.xs)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBlue.opacity(0.1))
                    )
            }
            
            Text("当前语言: \(currentLanguageDisplayName)")
                .captionText()
                .foregroundColor(.textSecondary)
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
    }
    
    private var currentLanguageDisplayName: String {
        return localizationService.availableLanguages
            .first { $0.code == localizationService.currentLanguage }?
            .nativeName ?? localizationService.currentLanguage
    }
}

// MARK: - Preview
#Preview {
    LocalizationDemoView()
        .environment(\.diContainer, DIContainer.shared)
}

#Preview("Status View") {
    LocalizationStatusView()
        .environment(\.diContainer, DIContainer.shared)
        .frame(width: 300)
}
