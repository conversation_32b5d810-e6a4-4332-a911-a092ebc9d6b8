//
//  LanguageSwitcher.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//

import SwiftUI

struct LanguageSwitcher: View {
    @Environment(\.localizationService) private var localizationService
    @State private var selectedLanguage: String
    @State private var showingTranslationOption = false
    
    let onLanguageChange: (String) -> Void
    
    init(currentLanguage: String, onLanguageChange: @escaping (String) -> Void) {
        self._selectedLanguage = State(initialValue: currentLanguage)
        self.onLanguageChange = onLanguageChange
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                Text("settings.language".localized)
                    .frame(width: 120, alignment: .leading)
                
                languagePicker
                
                // Auto-translate toggle
                Toggle("", isOn: $showingTranslationOption)
                    .toggleStyle(.switch)
                    .scaleEffect(0.8)
                    .help("Enable automatic translation for missing strings")
                
                Spacer()
            }
            
            if showingTranslationOption {
                translationOptions
            }
        }
    }
    
    private var languagePicker: some View {
        Picker("Language", selection: $selectedLanguage) {
            ForEach(localizationService.availableLanguages, id: \.code) { language in
                HStack {
                    Text(language.nativeName)
                    Spacer()
                    Text(language.name)
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
                .tag(language.code)
            }
        }
        .pickerStyle(.menu)
        .frame(width: 200)
        .onChange(of: selectedLanguage) { _, newValue in
            onLanguageChange(newValue)
        }
    }
    
    private var translationOptions: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text("Translation Options")
                .font(.caption)
                .foregroundColor(.textSecondary)
            
            HStack {
                Button("Translate Missing Strings") {
                    translateMissingStrings()
                }
                .buttonStyle(.bordered)
                .disabled(selectedLanguage == "en")
                
                Button("Test Translation") {
                    testTranslation()
                }
                .buttonStyle(.bordered)
                
                Spacer()
            }
            
            Text("Auto-translation uses macOS 15.0+ Translation framework")
                .font(.caption2)
                .foregroundColor(.textSecondary)
        }
        .padding(.leading, 120)
    }
    
    private func translateMissingStrings() {
        Task {
            // Example: Translate some common strings
            let testStrings = [
                "Welcome to MCP Copilot",
                "Settings",
                "Language",
                "About"
            ]
            
            let translatedStrings = await localizationService.translateTexts(testStrings, from: "en")
            
            // In a real implementation, you would update the localization files
            print("Translated strings: \(translatedStrings)")
        }
    }
    
    private func testTranslation() {
        Task {
            let testText = "Welcome to MCP Copilot"
            let translated = await localizationService.translateText(testText, from: "en")
            print("Test translation: \(testText) -> \(translated)")
        }
    }
}

#Preview {
    LanguageSwitcher(currentLanguage: "en") { _ in }
        .padding()
}