//
//  PerformanceMonitoringView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct PerformanceMonitoringView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var performanceService: PerformanceMonitoringService
    
    @State private var showingDetails = false
    @State private var selectedMetric: String?
    
    // MARK: - Initialization
    init() {
        self._performanceService = StateObject(wrappedValue: DIContainer.shared.performanceMonitoringService as! PerformanceMonitoringService)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            // 标题和控制
            headerSection
            
            // 实时指标
            metricsSection
            
            // 自定义指标
            customMetricsSection
            
            // 执行时间
            executionTimesSection
            
            Spacer()
        }
        .contentPadding()
        .navigationTitle("性能监控")
        .onAppear {
            performanceService.startMonitoring()
        }
        .onDisappear {
            performanceService.stopMonitoring()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text("性能监控")
                    .pageTitle()
                
                Text(performanceService.isMonitoring ? "监控中..." : "已停止")
                    .captionText()
                    .foregroundColor(performanceService.isMonitoring ? .statusSuccess : .textSecondary)
            }
            
            Spacer()
            
            HStack(spacing: Spacing.sm) {
                Button(performanceService.isMonitoring ? "停止监控" : "开始监控") {
                    if performanceService.isMonitoring {
                        performanceService.stopMonitoring()
                    } else {
                        performanceService.startMonitoring()
                    }
                }
                .buttonStyle(.bordered)
                .foregroundColor(performanceService.isMonitoring ? .statusError : .statusSuccess)
                
                Button("详细信息", systemImage: "info.circle") {
                    showingDetails = true
                }
                .buttonStyle(.bordered)
            }
        }
        .sheet(isPresented: $showingDetails) {
            PerformanceDetailsView(metrics: performanceService.performanceMetrics)
        }
    }
    
    // MARK: - Metrics Section
    private var metricsSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("系统指标")
                .sectionTitle()
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                MetricCard(
                    title: "CPU 使用率",
                    value: String(format: "%.1f%%", performanceService.performanceMetrics.cpuUsage),
                    progress: performanceService.performanceMetrics.cpuUsage / 100.0,
                    color: .chartBlue,
                    isWarning: performanceService.performanceMetrics.cpuUsage > performanceService.cpuWarningThreshold
                )
                
                MetricCard(
                    title: "内存使用",
                    value: String(format: "%.1f%%", performanceService.performanceMetrics.memoryUsage),
                    progress: performanceService.performanceMetrics.memoryUsage / 100.0,
                    color: .chartGreen,
                    isWarning: performanceService.performanceMetrics.memoryUsage > performanceService.memoryWarningThreshold
                )
                
                MetricCard(
                    title: "内存压力",
                    value: performanceService.performanceMetrics.memoryPressure.displayName,
                    progress: memoryPressureProgress,
                    color: memoryPressureColor,
                    isWarning: performanceService.performanceMetrics.memoryPressure != .normal
                )
                
                MetricCard(
                    title: "磁盘使用",
                    value: String(format: "%.1f%%", performanceService.performanceMetrics.diskUsage),
                    progress: performanceService.performanceMetrics.diskUsage / 100.0,
                    color: .chartOrange,
                    isWarning: performanceService.performanceMetrics.diskUsage > 90.0
                )
            }
        }
    }
    
    // MARK: - Custom Metrics Section
    private var customMetricsSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Text("自定义指标")
                    .sectionTitle()
                
                Spacer()
                
                Button("添加指标", systemImage: "plus") {
                    // TODO: 添加自定义指标
                }
                .buttonStyle(.bordered)
            }
            
            if performanceService.performanceMetrics.customMetrics.isEmpty {
                Text("暂无自定义指标")
                    .captionText()
                    .frame(maxWidth: .infinity)
                    .cardPadding()
                    .background(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .fill(Color.cardBackground)
                            .overlay(
                                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                    .stroke(Color.cardBorder, lineWidth: 1)
                            )
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: Spacing.sm) {
                    ForEach(Array(performanceService.performanceMetrics.customMetrics.keys.sorted()), id: \.self) { key in
                        if let metric = performanceService.performanceMetrics.customMetrics[key] {
                            CustomMetricCard(name: key, metric: metric)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Execution Times Section
    private var executionTimesSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("执行时间")
                .sectionTitle()
            
            if performanceService.performanceMetrics.executionTimes.isEmpty {
                Text("暂无执行时间记录")
                    .captionText()
                    .frame(maxWidth: .infinity)
                    .cardPadding()
                    .background(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .fill(Color.cardBackground)
                            .overlay(
                                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                    .stroke(Color.cardBorder, lineWidth: 1)
                            )
                    )
            } else {
                VStack(spacing: Spacing.sm) {
                    ForEach(Array(performanceService.performanceMetrics.executionTimes.keys.sorted()), id: \.self) { operation in
                        if let time = performanceService.performanceMetrics.executionTimes[operation] {
                            ExecutionTimeRow(operation: operation, time: time)
                        }
                    }
                }
                .cardPadding()
                .background(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .fill(Color.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                .stroke(Color.cardBorder, lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    private var memoryPressureProgress: Double {
        switch performanceService.performanceMetrics.memoryPressure {
        case .normal: return 0.3
        case .warning: return 0.7
        case .critical: return 1.0
        }
    }
    
    private var memoryPressureColor: Color {
        switch performanceService.performanceMetrics.memoryPressure {
        case .normal: return .chartGreen
        case .warning: return .chartOrange
        case .critical: return .chartRed
        }
    }
}

// MARK: - Metric Card
private struct MetricCard: View {
    let title: String
    let value: String
    let progress: Double
    let color: Color
    let isWarning: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                Text(title)
                    .labelText()
                
                Spacer()
                
                if isWarning {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.statusWarning)
                        .font(.caption)
                }
            }
            
            Text(value)
                .numberText()
                .foregroundColor(isWarning ? .statusWarning : .textPrimary)
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: isWarning ? .statusWarning : color))
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(isWarning ? Color.statusWarning : Color.cardBorder, lineWidth: 1)
                )
        )
    }
}

// MARK: - Custom Metric Card
private struct CustomMetricCard: View {
    let name: String
    let metric: MetricValue
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text(name)
                .labelText()
                .lineLimit(1)
            
            Text("\(metric.value, specifier: "%.2f") \(metric.unit)")
                .numberText()
            
            Text(formatTimestamp(metric.timestamp))
                .captionText()
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.backgroundSecondary)
        )
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
}

// MARK: - Execution Time Row
private struct ExecutionTimeRow: View {
    let operation: String
    let time: TimeInterval
    
    var body: some View {
        HStack {
            Text(operation)
                .bodyText()
            
            Spacer()
            
            Text(String(format: "%.3f s", time))
                .numberText()
                .foregroundColor(time > 1.0 ? .statusWarning : .textPrimary)
        }
    }
}

// MARK: - Performance Details View
private struct PerformanceDetailsView: View {
    let metrics: PerformanceSnapshot
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: Spacing.lg) {
                    Text("详细性能信息")
                        .pageTitle()
                    
                    // TODO: 添加详细的性能图表和历史数据
                    Text("详细性能监控功能开发中...")
                        .bodyText()
                }
                .contentPadding()
            }
            .navigationTitle("性能详情")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
    }
}

// MARK: - Preview
#Preview {
    PerformanceMonitoringView()
        .environment(\.diContainer, DIContainer.shared)
}
