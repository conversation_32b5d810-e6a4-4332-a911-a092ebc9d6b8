//
//  HomeView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct HomeView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var viewModel: HomeViewModel
    
    // MARK: - Initialization
    init() {
        self._viewModel = StateObject(wrappedValue: DIContainer.shared.homeViewModel)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationSplitView {
            sidebarContent
        } detail: {
            detailContent
        }
        .navigationTitle("MCP Copilot")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button("添加场景", systemImage: "plus") {
                    viewModel.addNewScene()
                }
            }
        }
        .sheet(isPresented: $viewModel.showingAddScene) {
            SceneEditView(scene: nil) { scene in
                viewModel.saveScene(scene)
            }
        }
        .sheet(isPresented: $viewModel.showingEditScene) {
            if let scene = viewModel.selectedScene {
                SceneEditView(scene: scene) { updatedScene in
                    viewModel.saveScene(updatedScene)
                }
            }
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.dismissError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            viewModel.loadScenes()
        }
    }
    
    // MARK: - Sidebar Content
    private var sidebarContent: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            // 活跃场景
            if viewModel.hasActiveScenes {
                sectionHeader("运行中的场景")
                ForEach(viewModel.activeScenes, id: \.id) { scene in
                    SceneRowView(
                        scene: scene,
                        status: viewModel.getProcessStatus(for: scene),
                        isSelected: viewModel.selectedScene?.id == scene.id,
                        onSelect: { viewModel.selectScene(scene) },
                        onStart: { viewModel.startScene(scene) },
                        onStop: { viewModel.stopScene(scene) },
                        onRestart: { viewModel.restartScene(scene) },
                        onEdit: { viewModel.editScene(scene) },
                        onDelete: { viewModel.deleteScene(scene) }
                    )
                }
                
                Divider()
                    .padding(.vertical, Spacing.sm)
            }
            
            // 所有场景
            sectionHeader("所有场景")
            
            if viewModel.isLoading {
                ProgressView("加载中...")
                    .frame(maxWidth: .infinity)
                    .padding()
            } else if viewModel.scenes.isEmpty {
                emptyStateView
            } else {
                ForEach(viewModel.scenes, id: \.id) { scene in
                    SceneRowView(
                        scene: scene,
                        status: viewModel.getProcessStatus(for: scene),
                        isSelected: viewModel.selectedScene?.id == scene.id,
                        onSelect: { viewModel.selectScene(scene) },
                        onStart: { viewModel.startScene(scene) },
                        onStop: { viewModel.stopScene(scene) },
                        onRestart: { viewModel.restartScene(scene) },
                        onEdit: { viewModel.editScene(scene) },
                        onDelete: { viewModel.deleteScene(scene) }
                    )
                }
            }
            
            Spacer()
        }
        .sidebarPadding()
        .frame(minWidth: Spacing.Sidebar.minWidth, maxWidth: Spacing.Sidebar.maxWidth)
    }
    
    // MARK: - Detail Content
    private var detailContent: some View {
        Group {
            if let selectedScene = viewModel.selectedScene {
                SceneDetailView(scene: selectedScene)
            } else {
                welcomeView
            }
        }
        .contentPadding()
    }
    
    // MARK: - Helper Views
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .sectionTitle()
            .padding(.bottom, Spacing.xs)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: Spacing.md) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.textSecondary)
            
            Text("暂无场景")
                .bodyText()
            
            Text("点击上方的 + 按钮创建第一个场景")
                .captionText()
                .multilineTextAlignment(.center)
            
            Button("创建场景") {
                viewModel.addNewScene()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity)
        .padding()
    }
    
    private var welcomeView: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: "network")
                .font(.system(size: 64))
                .foregroundColor(.primaryBlue)
            
            Text("欢迎使用 MCP Copilot")
                .pageTitle()
            
            Text("选择左侧的场景来查看详细信息，或创建新的场景来开始使用。")
                .bodyText()
                .multilineTextAlignment(.center)
                .frame(maxWidth: 400)
            
            HStack(spacing: Spacing.md) {
                Button("创建场景") {
                    viewModel.addNewScene()
                }
                .buttonStyle(.borderedProminent)
                
                Button("查看文档") {
                    // TODO: 打开文档链接
                }
                .buttonStyle(.bordered)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Preview
#Preview {
    HomeView()
        .environment(\.diContainer, DIContainer.shared)
}
