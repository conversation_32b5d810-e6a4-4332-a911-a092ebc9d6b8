//
//  DebugFloatingPanel.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct DebugFloatingPanel: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var performanceService: PerformanceMonitoringService
    @StateObject private var loggingService: LoggingService
    
    @State private var isExpanded = false
    @State private var position: CGPoint = CGPoint(x: 50, y: 50)
    @State private var isDragging = false
    
    // MARK: - Initialization
    init() {
        self._performanceService = StateObject(wrappedValue: DIContainer.shared.performanceMonitoringService as! PerformanceMonitoringService)
        self._loggingService = StateObject(wrappedValue: DIContainer.shared.loggingService as! LoggingService)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            if isExpanded {
                expandedContent
            } else {
                collapsedContent
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 8)
        )
        .position(position)
        .draggable(
            onDragChanged: { translation in
                position = CGPoint(
                    x: position.x + translation.width,
                    y: position.y + translation.height
                )
            }
        )
        .onAppear {
            performanceService.startMonitoring()
        }
    }
    
    // MARK: - Collapsed Content
    private var collapsedContent: some View {
        Button(action: {
            withAnimation(.spring()) {
                isExpanded.toggle()
            }
        }) {
            HStack(spacing: Spacing.xs) {
                Image(systemName: "ladybug")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("DEBUG")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("\(Int(performanceService.performanceMetrics.cpuUsage))%")
                        .font(.system(size: 12, weight: .medium, design: .monospaced))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        }
        .buttonStyle(.plain)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(debugColor)
        )
    }
    
    // MARK: - Expanded Content
    private var expandedContent: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            // 标题栏
            headerBar
            
            Divider()
            
            // 性能指标
            performanceMetrics
            
            Divider()
            
            // 快速操作
            quickActions
            
            Divider()
            
            // 最近日志
            recentLogs
        }
        .padding(Spacing.sm)
        .frame(width: 280)
    }
    
    // MARK: - Header Bar
    private var headerBar: some View {
        HStack {
            HStack(spacing: Spacing.xs) {
                Image(systemName: "ladybug")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primaryBlue)
                
                Text("调试面板")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.textPrimary)
            }
            
            Spacer()
            
            Button(action: {
                withAnimation(.spring()) {
                    isExpanded = false
                }
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.textSecondary)
            }
            .buttonStyle(.plain)
        }
    }
    
    // MARK: - Performance Metrics
    private var performanceMetrics: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text("性能指标")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.textPrimary)
            
            VStack(spacing: 4) {
                metricRow("CPU", "\(Int(performanceService.performanceMetrics.cpuUsage))%", 
                         progress: performanceService.performanceMetrics.cpuUsage / 100.0,
                         color: .chartBlue)
                
                metricRow("内存", "\(Int(performanceService.performanceMetrics.memoryUsage))%", 
                         progress: performanceService.performanceMetrics.memoryUsage / 100.0,
                         color: .chartGreen)
                
                metricRow("磁盘", "\(Int(performanceService.performanceMetrics.diskUsage))%", 
                         progress: performanceService.performanceMetrics.diskUsage / 100.0,
                         color: .chartOrange)
            }
        }
    }
    
    // MARK: - Quick Actions
    private var quickActions: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text("快速操作")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.textPrimary)
            
            HStack(spacing: Spacing.xs) {
                debugButton("清空日志", systemImage: "trash") {
                    loggingService.clearLogs()
                }
                
                debugButton("导出日志", systemImage: "square.and.arrow.up") {
                    Task {
                        try? await loggingService.exportLogs()
                    }
                }
                
                debugButton("重启监控", systemImage: "arrow.clockwise") {
                    performanceService.stopMonitoring()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        performanceService.startMonitoring()
                    }
                }
            }
        }
    }
    
    // MARK: - Recent Logs
    private var recentLogs: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text("最近日志")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.textPrimary)
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 2) {
                    ForEach(Array(loggingService.logEntries.suffix(5)), id: \.id) { entry in
                        logEntryRow(entry)
                    }
                }
            }
            .frame(height: 80)
        }
    }
    
    // MARK: - Helper Views
    private func metricRow(_ label: String, _ value: String, progress: Double, color: Color) -> some View {
        HStack(spacing: Spacing.xs) {
            Text(label)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.textSecondary)
                .frame(width: 30, alignment: .leading)
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .frame(height: 4)
            
            Text(value)
                .font(.system(size: 10, weight: .medium, design: .monospaced))
                .foregroundColor(.textPrimary)
                .frame(width: 35, alignment: .trailing)
        }
    }
    
    private func debugButton(_ title: String, systemImage: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 2) {
                Image(systemName: systemImage)
                    .font(.system(size: 12, weight: .medium))
                
                Text(title)
                    .font(.system(size: 8, weight: .medium))
            }
            .foregroundColor(.textPrimary)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.backgroundSecondary)
            )
        }
        .buttonStyle(.plain)
    }
    
    private func logEntryRow(_ entry: LogEntry) -> some View {
        HStack(spacing: 4) {
            Circle()
                .fill(Color.forLogLevel(entry.level))
                .frame(width: 6, height: 6)
            
            Text(entry.message)
                .font(.system(size: 9, design: .monospaced))
                .foregroundColor(.textPrimary)
                .lineLimit(1)
                .truncationMode(.tail)
        }
    }
    
    // MARK: - Computed Properties
    private var debugColor: Color {
        let cpuUsage = performanceService.performanceMetrics.cpuUsage
        
        if cpuUsage > 80 {
            return .statusError
        } else if cpuUsage > 60 {
            return .statusWarning
        } else {
            return .primaryBlue
        }
    }
}

// MARK: - Debug Panel Modifier
struct DebugPanelModifier: ViewModifier {
    @State private var showDebugPanel = false
    
    func body(content: Content) -> some View {
        content
            .overlay(alignment: .topTrailing) {
                if showDebugPanel {
                    DebugFloatingPanel()
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .onKeyPress(.init("d"), phases: .down) { _ in
                #if DEBUG
                withAnimation(.spring()) {
                    showDebugPanel.toggle()
                }
                return .handled
                #else
                return .ignored
                #endif
            }
    }
}

extension View {
    func debugPanel() -> some View {
        self.modifier(DebugPanelModifier())
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.backgroundPrimary
            .ignoresSafeArea()
        
        DebugFloatingPanel()
    }
    .environment(\.diContainer, DIContainer.shared)
    .frame(width: 800, height: 600)
}
