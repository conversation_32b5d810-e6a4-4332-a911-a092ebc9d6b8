//
//  ConfigurationView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI
import UniformTypeIdentifiers

struct ConfigurationView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var viewModel: ConfigurationViewModel
    
    @State private var showingValidationResults = false
    @State private var validationIssues: [ConfigurationIssue] = []
    
    // MARK: - Initialization
    init() {
        self._viewModel = StateObject(wrappedValue: DIContainer.shared.configurationViewModel)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationSplitView {
            sidebarContent
        } detail: {
            detailContent
        }
        .navigationTitle("配置管理")
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                Button("验证配置", systemImage: "checkmark.shield") {
                    validateConfiguration()
                }
                .buttonStyle(.bordered)
                
                Menu("导入导出", systemImage: "arrow.up.arrow.down") {
                    Button("导出所有配置", systemImage: "square.and.arrow.up") {
                        exportAllConfigurations()
                    }
                    
                    Button("导入配置", systemImage: "square.and.arrow.down") {
                        viewModel.importConfiguration()
                    }
                    
                    Divider()
                    
                    if let selectedScene = viewModel.selectedScene {
                        Button("导出当前场景", systemImage: "doc.badge.arrow.up") {
                            exportSelectedScene(selectedScene)
                        }
                        
                        Button("复制当前场景", systemImage: "doc.on.doc") {
                            duplicateSelectedScene(selectedScene)
                        }
                    }
                }
                .buttonStyle(.bordered)
            }
        }
        .sheet(isPresented: $showingValidationResults) {
            ValidationResultsView(issues: validationIssues)
        }
        .fileImporter(
            isPresented: $viewModel.showingImportDialog,
            allowedContentTypes: [.json],
            allowsMultipleSelection: false
        ) { result in
            handleImport(result)
        }
        .fileExporter(
            isPresented: $viewModel.showingExportDialog,
            document: ConfigurationDocument(),
            contentType: .json,
            defaultFilename: "mcpcopilot_config"
        ) { result in
            handleExport(result)
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.dismissMessages()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("成功", isPresented: .constant(viewModel.successMessage != nil)) {
            Button("确定") {
                viewModel.dismissMessages()
            }
        } message: {
            if let successMessage = viewModel.successMessage {
                Text(successMessage)
            }
        }
        .onAppear {
            viewModel.loadData()
        }
    }
    
    // MARK: - Sidebar Content
    private var sidebarContent: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("场景配置")
                .sectionTitle()
                .padding(.horizontal, Spacing.md)
            
            if viewModel.isLoading {
                ProgressView("加载中...")
                    .frame(maxWidth: .infinity)
                    .padding()
            } else if viewModel.scenes.isEmpty {
                emptyStateView
            } else {
                List(viewModel.scenes, id: \.id, selection: $viewModel.selectedScene) { scene in
                    ConfigurationSceneRow(scene: scene)
                        .tag(scene)
                }
                .listStyle(.sidebar)
            }
        }
        .frame(minWidth: Spacing.Sidebar.minWidth, maxWidth: Spacing.Sidebar.maxWidth)
    }
    
    // MARK: - Detail Content
    private var detailContent: some View {
        Group {
            if let selectedScene = viewModel.selectedScene {
                ConfigurationDetailView(scene: selectedScene)
            } else {
                configurationWelcomeView
            }
        }
        .contentPadding()
    }
    
    // MARK: - Helper Views
    private var emptyStateView: some View {
        VStack(spacing: Spacing.md) {
            Image(systemName: "gear.badge")
                .font(.system(size: 48))
                .foregroundColor(.textSecondary)
            
            Text("暂无配置")
                .bodyText()
            
            Text("请先创建场景来管理配置")
                .captionText()
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
    }
    
    private var configurationWelcomeView: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: "gear.badge")
                .font(.system(size: 64))
                .foregroundColor(.primaryBlue)
            
            Text("配置管理")
                .pageTitle()
            
            Text("选择左侧的场景来查看和编辑配置，或使用工具栏的功能来导入导出配置。")
                .bodyText()
                .multilineTextAlignment(.center)
                .frame(maxWidth: 400)
            
            HStack(spacing: Spacing.md) {
                Button("验证配置") {
                    validateConfiguration()
                }
                .buttonStyle(.borderedProminent)
                
                Button("导入配置") {
                    viewModel.importConfiguration()
                }
                .buttonStyle(.bordered)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Actions
    private func validateConfiguration() {
        Task {
            validationIssues = await viewModel.validateConfiguration()
            showingValidationResults = true
        }
    }
    
    private func exportAllConfigurations() {
        Task {
            do {
                let url = try await viewModel.exportAllConfigurations()
                // TODO: 显示导出成功的提示
                print("Exported to: \(url)")
            } catch {
                viewModel.errorMessage = "导出失败: \(error.localizedDescription)"
            }
        }
    }
    
    private func exportSelectedScene(_ scene: MCPScene) {
        Task {
            do {
                let url = try await viewModel.exportSceneConfiguration(scene)
                // TODO: 显示导出成功的提示
                print("Scene exported to: \(url)")
            } catch {
                viewModel.errorMessage = "导出场景失败: \(error.localizedDescription)"
            }
        }
    }
    
    private func duplicateSelectedScene(_ scene: MCPScene) {
        Task {
            do {
                try await viewModel.duplicateScene(scene)
            } catch {
                viewModel.errorMessage = "复制场景失败: \(error.localizedDescription)"
            }
        }
    }
    
    private func handleImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            
            Task {
                do {
                    try await viewModel.importAllConfigurations(from: url)
                } catch {
                    viewModel.errorMessage = "导入失败: \(error.localizedDescription)"
                }
            }
            
        case .failure(let error):
            viewModel.errorMessage = "选择文件失败: \(error.localizedDescription)"
        }
    }
    
    private func handleExport(_ result: Result<URL, Error>) {
        switch result {
        case .success(let url):
            print("Exported to: \(url)")
            
        case .failure(let error):
            viewModel.errorMessage = "导出失败: \(error.localizedDescription)"
        }
    }
}

// MARK: - Configuration Scene Row
private struct ConfigurationSceneRow: View {
    let scene: MCPScene
    
    var body: some View {
        HStack(spacing: Spacing.sm) {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(scene.name)
                    .labelText()
                    .lineLimit(1)
                
                HStack(spacing: Spacing.xs) {
                    Text("端口: \(scene.port)")
                        .captionText()
                    
                    if !scene.servers.isEmpty {
                        Text("•")
                            .captionText()
                        
                        Text("\(scene.servers.count) 个服务")
                            .captionText()
                    }
                    
                    if !scene.environment.isEmpty {
                        Text("•")
                            .captionText()
                        
                        Text("\(scene.environment.count) 个环境变量")
                            .captionText()
                    }
                }
            }
            
            Spacer()
            
            if scene.isActive {
                Circle()
                    .fill(Color.statusSuccess)
                    .frame(width: 8, height: 8)
            }
        }
        .contentShape(Rectangle())
    }
}

// MARK: - Configuration Detail View
private struct ConfigurationDetailView: View {
    let scene: MCPScene
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: Spacing.Content.sectionSpacing) {
                // 场景信息
                sceneInfoSection
                
                // JSON 配置预览
                jsonPreviewSection
                
                // 环境变量
                environmentSection
                
                // 服务器配置
                serversSection
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    private var sceneInfoSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("场景信息")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                configRow("名称", scene.name)
                configRow("端口", "\(scene.port)")
                configRow("状态", scene.isActive ? "运行中" : "已停止")
                configRow("服务器数量", "\(scene.servers.count)")
                configRow("环境变量数量", "\(scene.environment.count)")
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    private var jsonPreviewSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Text("JSON 配置")
                    .sectionTitle()
                
                Spacer()
                
                Button("复制", systemImage: "doc.on.doc") {
                    copyJSONToClipboard()
                }
                .buttonStyle(.bordered)
            }
            
            ScrollView {
                Text(generateJSONPreview())
                    .codeText()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
            }
            .frame(height: 200)
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.backgroundSecondary)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    private var environmentSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("环境变量")
                .sectionTitle()
            
            if scene.environment.isEmpty {
                Text("暂无环境变量")
                    .captionText()
                    .frame(maxWidth: .infinity)
                    .cardPadding()
                    .background(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .fill(Color.cardBackground)
                            .overlay(
                                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                    .stroke(Color.cardBorder, lineWidth: 1)
                            )
                    )
            } else {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    ForEach(Array(scene.environment.keys.sorted()), id: \.self) { key in
                        if let value = scene.environment[key] {
                            configRow(key, value, isCode: true)
                        }
                    }
                }
                .cardPadding()
                .background(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .fill(Color.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                .stroke(Color.cardBorder, lineWidth: 1)
                        )
                )
            }
        }
    }
    
    private var serversSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("MCP 服务器")
                .sectionTitle()
            
            if scene.servers.isEmpty {
                Text("暂无 MCP 服务器")
                    .captionText()
                    .frame(maxWidth: .infinity)
                    .cardPadding()
                    .background(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .fill(Color.cardBackground)
                            .overlay(
                                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                    .stroke(Color.cardBorder, lineWidth: 1)
                            )
                    )
            } else {
                LazyVStack(spacing: Spacing.sm) {
                    ForEach(scene.servers, id: \.id) { server in
                        ServerConfigurationCard(server: server)
                    }
                }
            }
        }
    }
    
    private func configRow(_ label: String, _ value: String, isCode: Bool = false) -> some View {
        HStack {
            Text(label)
                .labelText()
                .frame(width: 120, alignment: .leading)
            
            if isCode {
                Text(value)
                    .codeText()
                    .foregroundColor(.primaryBlue)
            } else {
                Text(value)
                    .bodyText()
            }
            
            Spacer()
        }
    }
    
    private func generateJSONPreview() -> String {
        let exportData = SceneExportData(from: scene)
        
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            encoder.dateEncodingStrategy = .iso8601
            
            let data = try encoder.encode(exportData)
            return String(data: data, encoding: .utf8) ?? "无法生成 JSON"
        } catch {
            return "JSON 生成失败: \(error.localizedDescription)"
        }
    }
    
    private func copyJSONToClipboard() {
        let json = generateJSONPreview()
        NSPasteboard.general.setString(json, forType: .string)
    }
}

// MARK: - Server Configuration Card
private struct ServerConfigurationCard: View {
    let server: MCPServer
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                Text(server.name)
                    .labelText()
                
                Spacer()
                
                Text(server.type.displayName)
                    .captionText()
                    .padding(.horizontal, Spacing.xs)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBlue.opacity(0.1))
                    )
            }
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text("命名空间: \(server.namespace)")
                    .captionText()
                
                Text("命令: \(server.command)")
                    .codeText()
                
                if !server.arguments.isEmpty {
                    Text("参数: \(server.arguments.joined(separator: " "))")
                        .codeText()
                }
            }
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.backgroundSecondary)
        )
    }
}

// MARK: - Validation Results View
private struct ValidationResultsView: View {
    let issues: [ConfigurationIssue]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: Spacing.md) {
                if issues.isEmpty {
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 64))
                            .foregroundColor(.statusSuccess)
                        
                        Text("配置验证通过")
                            .pageTitle()
                        
                        Text("所有配置都是有效的，没有发现问题。")
                            .bodyText()
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    Text("发现 \(issues.count) 个问题")
                        .sectionTitle()
                        .padding(.horizontal, Spacing.md)
                    
                    List(issues) { issue in
                        ValidationIssueRow(issue: issue)
                    }
                }
            }
            .navigationTitle("配置验证结果")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
    }
}

// MARK: - Validation Issue Row
private struct ValidationIssueRow: View {
    let issue: ConfigurationIssue
    
    var body: some View {
        HStack(alignment: .top, spacing: Spacing.sm) {
            Image(systemName: severityIcon)
                .foregroundColor(severityColor)
                .font(.system(size: 16, weight: .medium))
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(issue.message)
                    .bodyText()
                
                Text(severityText)
                    .captionText()
                    .foregroundColor(severityColor)
            }
            
            Spacer()
        }
        .padding(.vertical, Spacing.xs)
    }
    
    private var severityIcon: String {
        switch issue.severity {
        case .error: return "xmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .info: return "info.circle.fill"
        }
    }
    
    private var severityColor: Color {
        switch issue.severity {
        case .error: return .statusError
        case .warning: return .statusWarning
        case .info: return .statusInfo
        }
    }
    
    private var severityText: String {
        switch issue.severity {
        case .error: return "错误"
        case .warning: return "警告"
        case .info: return "信息"
        }
    }
}

// MARK: - Configuration Document
private struct ConfigurationDocument: FileDocument {
    static var readableContentTypes: [UTType] { [.json] }
    
    var configuration: String = ""
    
    init() {}
    
    init(configuration: FileDocumentReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8) else {
            throw CocoaError(.fileReadCorruptFile)
        }
        self.configuration = string
    }
    
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = self.configuration.data(using: .utf8) ?? Data()
        return .init(regularFileWithContents: data)
    }
}

// MARK: - Preview
#Preview {
    ConfigurationView()
        .environment(\.diContainer, DIContainer.shared)
}
