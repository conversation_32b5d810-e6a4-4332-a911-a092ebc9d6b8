//
//  ConfigurationViewModel.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class ConfigurationViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var scenes: [MCPScene] = []
    @Published var selectedScene: MCPScene?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    @Published var showingImportDialog = false
    @Published var showingExportDialog = false
    
    // MARK: - Dependencies
    private let configurationRepository: ConfigurationRepositoryProtocol
    private let sceneRepository: SceneRepositoryProtocol
    private let securityService: SecurityServiceProtocol
    private let cacheService: CacheServiceProtocol
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        configurationRepository: ConfigurationRepositoryProtocol,
        sceneRepository: SceneRepositoryProtocol,
        securityService: SecurityServiceProtocol,
        cacheService: CacheServiceProtocol
    ) {
        self.configurationRepository = configurationRepository
        self.sceneRepository = sceneRepository
        self.securityService = securityService
        self.cacheService = cacheService
        
        setupBindings()
        loadData()
    }
    
    // MARK: - Public Methods
    func loadData() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                try await sceneRepository.loadScenes()
                await MainActor.run {
                    self.scenes = sceneRepository.scenes
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载配置失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func selectScene(_ scene: MCPScene) {
        selectedScene = scene
    }
    
    func exportConfiguration() {
        showingExportDialog = true
    }
    
    func importConfiguration() {
        showingImportDialog = true
    }
    
    func exportSceneConfiguration(_ scene: MCPScene) async throws -> URL {
        let exportData = SceneExportData(from: scene)
        
        // 创建导出文件
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let exportURL = documentsPath.appendingPathComponent("MCPCopilot/exports")
        
        // 确保导出目录存在
        try FileManager.default.createDirectory(at: exportURL, withIntermediateDirectories: true)
        
        let fileName = "\(scene.name.replacingOccurrences(of: " ", with: "_"))_\(Date().timeIntervalSince1970).json"
        let fileURL = exportURL.appendingPathComponent(fileName)
        
        // 编码并写入文件
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601
        
        let data = try encoder.encode(exportData)
        try data.write(to: fileURL)
        
        return fileURL
    }
    
    func importSceneConfiguration(from url: URL) async throws {
        let data = try Data(contentsOf: url)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importData = try decoder.decode(SceneExportData.self, from: data)
        let scene = importData.toMCPScene()
        
        // 检查是否存在同名场景
        if scenes.contains(where: { $0.name == scene.name }) {
            scene.name = "\(scene.name) (导入)"
        }
        
        try await sceneRepository.saveScene(scene)
        
        await MainActor.run {
            self.scenes = sceneRepository.scenes
            self.successMessage = "场景配置导入成功"
        }
    }
    
    func exportAllConfigurations() async throws -> URL {
        let exportData = AllConfigurationsExportData(
            scenes: scenes.map { SceneExportData(from: $0) },
            appConfiguration: configurationRepository.appConfiguration,
            exportDate: Date(),
            version: "1.0.0"
        )
        
        // 创建导出文件
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let exportURL = documentsPath.appendingPathComponent("MCPCopilot/exports")
        
        try FileManager.default.createDirectory(at: exportURL, withIntermediateDirectories: true)
        
        let fileName = "mcpcopilot_config_\(Date().timeIntervalSince1970).json"
        let fileURL = exportURL.appendingPathComponent(fileName)
        
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601
        
        let data = try encoder.encode(exportData)
        try data.write(to: fileURL)
        
        return fileURL
    }
    
    func importAllConfigurations(from url: URL) async throws {
        let data = try Data(contentsOf: url)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importData = try decoder.decode(AllConfigurationsExportData.self, from: data)
        
        // 导入应用配置
        try await configurationRepository.saveConfiguration(importData.appConfiguration)
        
        // 导入场景配置
        for sceneData in importData.scenes {
            let scene = sceneData.toMCPScene()
            
            // 检查是否存在同名场景
            if scenes.contains(where: { $0.name == scene.name }) {
                scene.name = "\(scene.name) (导入)"
            }
            
            try await sceneRepository.saveScene(scene)
        }
        
        await MainActor.run {
            self.scenes = sceneRepository.scenes
            self.successMessage = "所有配置导入成功"
        }
    }
    
    func duplicateScene(_ scene: MCPScene) async throws {
        let duplicatedScene = MCPScene(
            name: "\(scene.name) (副本)",
            port: scene.port + 1,
            isActive: false,
            environment: scene.environment
        )
        
        // 复制服务器
        for server in scene.servers {
            let duplicatedServer = MCPServer(
                name: server.name,
                namespace: server.namespace,
                type: server.type,
                command: server.command,
                arguments: server.arguments
            )
            duplicatedServer.scene = duplicatedScene
            duplicatedScene.servers.append(duplicatedServer)
        }
        
        try await sceneRepository.saveScene(duplicatedScene)
        
        await MainActor.run {
            self.scenes = sceneRepository.scenes
            self.successMessage = "场景复制成功"
        }
    }
    
    func validateConfiguration() async -> [ConfigurationIssue] {
        var issues: [ConfigurationIssue] = []
        
        // 检查端口冲突
        let portGroups = Dictionary(grouping: scenes) { $0.port }
        for (port, scenesWithPort) in portGroups {
            if scenesWithPort.count > 1 {
                issues.append(ConfigurationIssue(
                    type: .portConflict,
                    severity: .error,
                    message: "端口 \(port) 被多个场景使用: \(scenesWithPort.map { $0.name }.joined(separator: ", "))",
                    affectedScenes: scenesWithPort.map { $0.id }
                ))
            }
        }
        
        // 检查空场景
        for scene in scenes {
            if scene.servers.isEmpty {
                issues.append(ConfigurationIssue(
                    type: .emptyScene,
                    severity: .warning,
                    message: "场景 \"\(scene.name)\" 没有配置任何 MCP 服务器",
                    affectedScenes: [scene.id]
                ))
            }
        }
        
        // 检查无效的环境变量
        for scene in scenes {
            for (key, value) in scene.environment {
                if key.isEmpty || value.isEmpty {
                    issues.append(ConfigurationIssue(
                        type: .invalidEnvironmentVariable,
                        severity: .warning,
                        message: "场景 \"\(scene.name)\" 包含空的环境变量",
                        affectedScenes: [scene.id]
                    ))
                }
            }
        }
        
        return issues
    }
    
    func dismissMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        // Initialize scenes and rely on manual updates
        scenes = sceneRepository.scenes
        
        // Note: Direct publisher access doesn't work well with 'any Protocol'
        // For now, we'll rely on manual updates when modifying scenes
    }
}

// MARK: - Export/Import Data Models
struct SceneExportData: Codable {
    let name: String
    let port: Int
    let environment: [String: String]
    let servers: [MCPServerExportData]
    let exportDate: Date
    
    init(from scene: MCPScene) {
        self.name = scene.name
        self.port = scene.port
        self.environment = scene.environment
        self.servers = scene.servers.map { MCPServerExportData(from: $0) }
        self.exportDate = Date()
    }
    
    func toMCPScene() -> MCPScene {
        let scene = MCPScene(
            name: name,
            port: port,
            environment: environment
        )
        
        scene.servers = servers.map { $0.toMCPServer(scene: scene) }
        return scene
    }
}

struct MCPServerExportData: Codable {
    let name: String
    let namespace: String
    let type: MCPServerType
    let command: String
    let arguments: [String]
    
    init(from server: MCPServer) {
        self.name = server.name
        self.namespace = server.namespace
        self.type = server.type
        self.command = server.command
        self.arguments = server.arguments
    }
    
    func toMCPServer(scene: MCPScene) -> MCPServer {
        let server = MCPServer(
            name: name,
            namespace: namespace,
            type: type,
            command: command,
            arguments: arguments
        )
        server.scene = scene
        return server
    }
}

struct AllConfigurationsExportData: Codable {
    let scenes: [SceneExportData]
    let appConfiguration: AppConfiguration
    let exportDate: Date
    let version: String
}

// MARK: - Configuration Issues
struct ConfigurationIssue: Identifiable {
    let id = UUID()
    let type: IssueType
    let severity: Severity
    let message: String
    let affectedScenes: [UUID]
    
    enum IssueType {
        case portConflict
        case emptyScene
        case invalidEnvironmentVariable
        case missingCommand
        case invalidPort
    }
    
    enum Severity {
        case error
        case warning
        case info
        
        var color: String {
            switch self {
            case .error: return "red"
            case .warning: return "orange"
            case .info: return "blue"
            }
        }
    }
}
