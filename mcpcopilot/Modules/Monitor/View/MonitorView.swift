//
//  MonitorView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct MonitorView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var viewModel: MonitorViewModel
    
    // MARK: - Initialization
    init() {
        self._viewModel = StateObject(wrappedValue: DIContainer.shared.monitorViewModel)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            toolbar
            
            Divider()
            
            // 主内容
            HSplitView {
                // 左侧：系统监控
                systemMonitoringSection
                    .frame(minWidth: 300)
                
                // 右侧：日志
                logSection
                    .frame(minWidth: 400)
            }
        }
        .navigationTitle("监控")
        .onAppear {
            viewModel.startMonitoring()
        }
        .onDisappear {
            viewModel.stopMonitoring()
        }
    }
    
    // MARK: - Toolbar
    private var toolbar: some View {
        HStack {
            // 监控控制
            Button(viewModel.isMonitoring ? "停止监控" : "开始监控") {
                viewModel.toggleMonitoring()
            }
            .buttonStyle(.bordered)
            .foregroundColor(viewModel.isMonitoring ? .statusError : .statusSuccess)
            
            Spacer()
            
            // 统计信息
            HStack(spacing: Spacing.lg) {
                statusItem("运行进程", "\(viewModel.runningProcessesCount)")
                statusItem("日志条目", "\(viewModel.totalLogEntries)")
                statusItem("错误", "\(viewModel.errorLogEntries)", color: .statusError)
                statusItem("警告", "\(viewModel.warningLogEntries)", color: .statusWarning)
            }
            
            Spacer()
            
            // 操作按钮
            HStack(spacing: Spacing.sm) {
                Button("刷新", systemImage: "arrow.clockwise") {
                    viewModel.refreshData()
                }
                .buttonStyle(.bordered)
                
                Button("清空日志", systemImage: "trash") {
                    viewModel.clearLogs()
                }
                .buttonStyle(.bordered)
                .foregroundColor(.statusError)
                
                Button("导出日志", systemImage: "square.and.arrow.up") {
                    viewModel.exportLogs()
                }
                .buttonStyle(.bordered)
            }
        }
        .toolbarPadding()
        .frame(height: Spacing.Toolbar.height)
    }
    
    // MARK: - System Monitoring Section
    private var systemMonitoringSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("系统监控")
                .sectionTitle()
                .padding(.horizontal, Spacing.md)
            
            ScrollView {
                VStack(spacing: Spacing.md) {
                    // 系统资源卡片
                    systemResourceCard
                    
                    // 进程列表
                    processListCard
                }
                .contentPadding()
            }
        }
    }
    
    // MARK: - System Resource Card
    private var systemResourceCard: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("系统资源")
                .cardTitle()
            
            VStack(spacing: Spacing.sm) {
                resourceRow(
                    "CPU 使用率",
                    viewModel.formatCPUUsage(viewModel.systemResourceInfo.cpuUsage),
                    progress: viewModel.systemResourceInfo.cpuUsage / 100.0,
                    color: .chartBlue
                )
                
                resourceRow(
                    "内存使用",
                    "\(viewModel.formatMemoryUsage(viewModel.systemResourceInfo.usedMemory)) / \(viewModel.formatMemoryUsage(viewModel.systemResourceInfo.totalMemory))",
                    progress: viewModel.systemResourceInfo.memoryUsage / 100.0,
                    color: .chartGreen
                )
            }
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
    }
    
    // MARK: - Process List Card
    private var processListCard: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("运行中的进程")
                .cardTitle()
            
            if viewModel.processResourceInfos.isEmpty {
                Text("暂无运行中的进程")
                    .captionText()
                    .frame(maxWidth: .infinity)
                    .padding()
            } else {
                LazyVStack(spacing: Spacing.sm) {
                    ForEach(Array(viewModel.processResourceInfos.values), id: \.pid) { processInfo in
                        processRow(processInfo)
                    }
                }
            }
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
    }
    
    // MARK: - Log Section
    private var logSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 日志工具栏
            logToolbar
            
            Divider()
            
            // 日志列表
            logList
        }
    }
    
    // MARK: - Log Toolbar
    private var logToolbar: some View {
        HStack {
            Text("日志")
                .sectionTitle()
            
            Spacer()
            
            // 日志级别过滤
            Picker("日志级别", selection: $viewModel.selectedLogLevel) {
                ForEach(LogLevel.allCases, id: \.self) { level in
                    Text(level.displayName)
                        .tag(level)
                }
            }
            .pickerStyle(.menu)
            .frame(width: 100)
            
            // 搜索框
            TextField("搜索日志...", text: $viewModel.searchText)
                .textFieldStyle(.roundedBorder)
                .frame(width: 200)
        }
        .padding(.horizontal, Spacing.md)
        .padding(.vertical, Spacing.sm)
        .background(Color.backgroundSecondary)
    }
    
    // MARK: - Log List
    private var logList: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 0) {
                ForEach(viewModel.filteredLogEntries) { entry in
                    logEntryRow(entry)
                        .padding(.horizontal, Spacing.md)
                        .padding(.vertical, Spacing.xs)
                }
            }
        }
        .background(Color.backgroundPrimary)
    }
    
    // MARK: - Helper Views
    private func statusItem(_ label: String, _ value: String, color: Color = .textPrimary) -> some View {
        VStack(spacing: Spacing.xs) {
            Text(value)
                .numberText()
                .foregroundColor(color)
            
            Text(label)
                .captionText()
        }
    }
    
    private func resourceRow(_ label: String, _ value: String, progress: Double, color: Color) -> some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            HStack {
                Text(label)
                    .labelText()
                
                Spacer()
                
                Text(value)
                    .numberText()
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
        }
    }
    
    private func processRow(_ processInfo: ProcessResourceInfo) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text("PID: \(processInfo.pid)")
                    .labelText()
                
                Text("运行时间: \(viewModel.formatUptime(processInfo.uptime))")
                    .captionText()
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text("CPU: \(viewModel.formatCPUUsage(processInfo.cpuUsage))")
                    .numberText()
                
                Text("内存: \(viewModel.formatMemoryUsage(processInfo.memoryUsage))")
                    .numberText()
            }
            
            Circle()
                .fill(Color.forProcessStatus(processInfo.status))
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, Spacing.xs)
    }
    
    private func logEntryRow(_ entry: LogEntry) -> some View {
        HStack(alignment: .top, spacing: Spacing.sm) {
            // 时间戳
            Text(viewModel.formatTimestamp(entry.timestamp))
                .codeText()
                .foregroundColor(.textSecondary)
                .frame(width: 60, alignment: .leading)
            
            // 级别
            Text(entry.level.displayName)
                .captionText()
                .foregroundColor(Color.forLogLevel(entry.level))
                .frame(width: 40, alignment: .leading)
            
            // 来源
            if let source = entry.source {
                Text(source)
                    .captionText()
                    .foregroundColor(.textSecondary)
                    .frame(width: 100, alignment: .leading)
                    .lineLimit(1)
                    .truncationMode(.middle)
            }
            
            // 消息
            Text(entry.message)
                .bodyText()
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .contextMenu {
            Button("复制消息") {
                NSPasteboard.general.setString(entry.message, forType: .string)
            }
            
            Button("复制完整日志") {
                let fullLog = "[\(entry.level.displayName)] \(viewModel.formatTimestamp(entry.timestamp)) \(entry.source ?? "Unknown"): \(entry.message)"
                NSPasteboard.general.setString(fullLog, forType: .string)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    MonitorView()
        .environment(\.diContainer, DIContainer.shared)
        .frame(width: 1000, height: 600)
}
