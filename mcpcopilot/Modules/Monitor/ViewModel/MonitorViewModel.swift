//
//  MonitorViewModel.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class MonitorViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var systemResourceInfo: SystemResourceInfo = SystemResourceInfo()
    @Published var processResourceInfos: [UUID: ProcessResourceInfo] = [:]
    @Published var logEntries: [LogEntry] = []
    @Published var isMonitoring = false
    @Published var selectedLogLevel: LogLevel = .info
    @Published var searchText = ""
    
    // MARK: - Dependencies
    private let monitoringService: MonitoringServiceProtocol
    private let processService: ProcessServiceProtocol
    private let loggingService: LoggingServiceProtocol
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        monitoringService: MonitoringServiceProtocol,
        processService: ProcessServiceProtocol,
        loggingService: LoggingServiceProtocol
    ) {
        self.monitoringService = monitoringService
        self.processService = processService
        self.loggingService = loggingService
        
        setupBindings()
    }
    
    // MARK: - Public Methods
    func startMonitoring() {
        monitoringService.startMonitoring()
        isMonitoring = true
    }
    
    func stopMonitoring() {
        monitoringService.stopMonitoring()
        isMonitoring = false
    }
    
    func toggleMonitoring() {
        if isMonitoring {
            stopMonitoring()
        } else {
            startMonitoring()
        }
    }
    
    func clearLogs() {
        loggingService.clearLogs()
    }
    
    func exportLogs() {
        Task {
            do {
                let url = try await loggingService.exportLogs()
                // TODO: 显示导出成功的提示或打开文件位置
                print("Logs exported to: \(url)")
            } catch {
                print("Failed to export logs: \(error)")
            }
        }
    }
    
    func refreshData() {
        // 手动刷新数据
        Task {
            // 这里可以添加手动刷新逻辑
        }
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        // Initialize with current values
        systemResourceInfo = monitoringService.systemResourceInfo
        processResourceInfos = monitoringService.processResourceInfos
        logEntries = loggingService.logEntries
        
        // Note: Direct publisher access doesn't work well with 'any Protocol'
        // For now, we'll rely on manual updates through refresh methods
    }
}

// MARK: - Computed Properties
extension MonitorViewModel {
    
    var filteredLogEntries: [LogEntry] {
        var filtered = logEntries
        
        // 按日志级别过滤
        filtered = filtered.filter { entry in
            switch selectedLogLevel {
            case .debug:
                return true // 显示所有级别
            case .info:
                return entry.level != .debug
            case .warning:
                return entry.level == .warning || entry.level == .error
            case .error:
                return entry.level == .error
            }
        }
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            filtered = filtered.filter { entry in
                entry.message.localizedCaseInsensitiveContains(searchText) ||
                (entry.source?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
        
        return filtered
    }
    
    var runningProcessesCount: Int {
        return processService.runningProcesses.count
    }
    
    var totalLogEntries: Int {
        return logEntries.count
    }
    
    var errorLogEntries: Int {
        return logEntries.filter { $0.level == .error }.count
    }
    
    var warningLogEntries: Int {
        return logEntries.filter { $0.level == .warning }.count
    }
}

// MARK: - Formatting Helpers
extension MonitorViewModel {
    
    func formatCPUUsage(_ usage: Double) -> String {
        return String(format: "%.1f%%", usage)
    }
    
    func formatMemoryUsage(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    func formatMemoryPercentage(_ used: UInt64, total: UInt64) -> String {
        guard total > 0 else { return "0%" }
        let percentage = Double(used) / Double(total) * 100.0
        return String(format: "%.1f%%", percentage)
    }
    
    func formatUptime(_ uptime: TimeInterval) -> String {
        let hours = Int(uptime) / 3600
        let minutes = (Int(uptime) % 3600) / 60
        let seconds = Int(uptime) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
}
