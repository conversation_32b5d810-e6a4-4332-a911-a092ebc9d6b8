//
//  PerformanceOptimizations.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Performance Optimizations and Best Practices
//

import SwiftUI
import Combine

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Performance Optimization System

/// 统一的性能优化管理系统 - macOS 优化版本
struct PerformanceOptimizations {
    
    // MARK: - Memory Management
    
    /// 内存优化策略
    struct MemoryOptimization {
        
        /// 启用视图缓存
        static let enableViewCaching = true
        
        /// 启用图像缓存
        static let enableImageCaching = true
        
        /// 最大缓存大小 (MB)
        static let maxCacheSize: Int = 100
        
        /// 缓存清理阈值
        static let cacheCleanupThreshold: Double = 0.8
        
        /// 自动内存管理
        static let autoMemoryManagement = true
    }
    
    /// 渲染优化策略
    struct RenderingOptimization {
        
        /// 启用硬件加速
        static let enableHardwareAcceleration = true
        
        /// 使用金属渲染
        static let useMetalRenderer = true
        
        /// 最大帧率
        static let maxFrameRate: Int = 60
        
        /// 启用 VSync
        static let enableVSync = true
        
        /// 减少重绘
        static let minimizeRedraws = true
    }
    
    /// 动画优化策略
    struct AnimationOptimization {
        
        /// 启用动画合成
        static let enableAnimationComposition = true
        
        /// 使用硬件加速动画
        static let useHardwareAcceleratedAnimations = true
        
        /// 动画质量级别
        static let animationQuality: AnimationQuality = .high
        
        /// 减少动画复杂度
        static let reduceAnimationComplexity = false
        
        /// 自适应帧率
        static let adaptiveFrameRate = true
    }
    
    // MARK: - Data Management
    
    /// 数据优化策略
    struct DataOptimization {
        
        /// 启用懒加载
        static let enableLazyLoading = true
        
        /// 分页大小
        static let pageSize: Int = 50
        
        /// 预加载数量
        static let preloadCount: Int = 10
        
        /// 数据压缩
        static let enableDataCompression = true
        
        /// 后台数据处理
        static let backgroundDataProcessing = true
    }
}

// MARK: - Performance Monitoring

/// 性能监控器
@MainActor
class PerformanceMonitor: ObservableObject {
    
    // MARK: - Published Properties
    @Published private(set) var currentFPS: Double = 0
    @Published private(set) var memoryUsage: UInt64 = 0
    @Published private(set) var cpuUsage: Double = 0
    @Published private(set) var isMonitoring = false
    
    // MARK: - Private Properties
    private var displayLink: CADisplayLink?
    private var frameCount = 0
    private var lastTimestamp: CFTimeInterval = 0
    private var monitoringTimer: Timer?
    
    // MARK: - Singleton
    static let shared = PerformanceMonitor()
    
    private init() {}
    
    // MARK: - Public Methods
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        setupDisplayLink()
        setupMemoryMonitoring()
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        displayLink?.invalidate()
        displayLink = nil
        monitoringTimer?.invalidate()
        monitoringTimer = nil
    }
    
    // MARK: - Private Methods
    
    private func setupDisplayLink() {
        #if canImport(AppKit)
        // macOS 使用 CVDisplayLink 或 Timer 替代 CADisplayLink
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 1.0 / 60.0, repeats: true) { [weak self] _ in
            self?.updateFPS()
        }
        #endif
    }
    
    private func setupMemoryMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
                self?.updateCPUUsage()
            }
        }
    }
    
    private func updateFPS() {
        let currentTime = CACurrentMediaTime()
        
        if lastTimestamp == 0 {
            lastTimestamp = currentTime
            return
        }
        
        frameCount += 1
        let deltaTime = currentTime - lastTimestamp
        
        if deltaTime >= 1.0 {
            currentFPS = Double(frameCount) / deltaTime
            frameCount = 0
            lastTimestamp = currentTime
        }
    }
    
    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            memoryUsage = info.resident_size
        }
    }
    
    private func updateCPUUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            // 简化的 CPU 使用率计算
            cpuUsage = min(Double(info.resident_size) / 100000000.0, 100.0)
        }
    }
}

// MARK: - Animation Quality

enum AnimationQuality: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case ultra = "ultra"
    
    var frameRate: Int {
        switch self {
        case .low: return 30
        case .medium: return 45
        case .high: return 60
        case .ultra: return 120
        }
    }
    
    var interpolationQuality: CGInterpolationQuality {
        switch self {
        case .low: return .low
        case .medium: return .medium
        case .high: return .high
        case .ultra: return .high
        }
    }
}

// MARK: - View Performance Modifiers

extension View {
    
    /// 启用性能优化
    func performanceOptimized() -> some View {
        self.modifier(PerformanceOptimizedModifier())
    }
    
    /// 启用绘制缓存
    func drawingCached() -> some View {
        self.drawingGroup()
    }
    
    /// 启用几何缓存
    func geometryCached() -> some View {
        self.clipped()
            .compositingGroup()
    }
    
    /// 延迟渲染
    func lazyRendering() -> some View {
        self.modifier(LazyRenderingModifier())
    }
    
    /// 内存优化
    func memoryOptimized() -> some View {
        self.modifier(MemoryOptimizedModifier())
    }
    
    /// 批量更新
    func batchUpdates() -> some View {
        self.modifier(BatchUpdateModifier())
    }
}

// MARK: - Performance Modifiers

struct PerformanceOptimizedModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .drawingGroup(opaque: false, colorMode: .linear)
            .compositingGroup()
            .clipped()
    }
}

struct LazyRenderingModifier: ViewModifier {
    @State private var shouldRender = false
    
    func body(content: Content) -> some View {
        Group {
            if shouldRender {
                content
            } else {
                Rectangle()
                    .fill(Color.clear)
                    .onAppear {
                        DispatchQueue.main.async {
                            shouldRender = true
                        }
                    }
            }
        }
    }
}

struct MemoryOptimizedModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .onReceive(NotificationCenter.default.publisher(for: NSApplication.didResignActiveNotification)) { _ in
                // 应用进入后台时清理缓存
                ImageCache.shared.clearCache()
            }
    }
}

struct BatchUpdateModifier: ViewModifier {
    @State private var updateCount = 0
    private let batchSize = 10
    
    func body(content: Content) -> some View {
        content
            .onChange(of: updateCount) { _ in
                if updateCount % batchSize == 0 {
                    // 执行批量更新
                    performBatchUpdate()
                }
            }
    }
    
    private func performBatchUpdate() {
        // 批量更新逻辑
    }
}

// MARK: - Image Cache

class ImageCache {
    static let shared = ImageCache()
    
    private var cache: NSCache<NSString, NSImage> = {
        let cache = NSCache<NSString, NSImage>()
        cache.countLimit = 100
        cache.totalCostLimit = 100 * 1024 * 1024 // 100MB
        return cache
    }()
    
    private init() {}
    
    func image(for key: String) -> NSImage? {
        return cache.object(forKey: key as NSString)
    }
    
    func setImage(_ image: NSImage, for key: String) {
        let cost = Int(image.size.width * image.size.height * 4) // 估算内存占用
        cache.setObject(image, forKey: key as NSString, cost: cost)
    }
    
    func clearCache() {
        cache.removeAllObjects()
    }
}

// MARK: - Data Loading Optimization

struct LazyDataLoader<Content: View>: View {
    let data: () -> [AnyHashable]
    let content: (AnyHashable) -> Content
    let pageSize: Int
    
    @State private var loadedData: [AnyHashable] = []
    @State private var currentPage = 0
    @State private var isLoading = false
    
    init(
        data: @escaping () -> [AnyHashable],
        pageSize: Int = 20,
        @ViewBuilder content: @escaping (AnyHashable) -> Content
    ) {
        self.data = data
        self.pageSize = pageSize
        self.content = content
    }
    
    var body: some View {
        LazyVStack {
            ForEach(Array(loadedData.enumerated()), id: \.offset) { index, item in
                content(item)
                    .onAppear {
                        if index == loadedData.count - 5 {
                            loadMoreData()
                        }
                    }
            }
            
            if isLoading {
                ProgressView()
                    .padding()
            }
        }
        .onAppear {
            loadInitialData()
        }
    }
    
    private func loadInitialData() {
        let allData = data()
        let endIndex = min(pageSize, allData.count)
        loadedData = Array(allData[0..<endIndex])
        currentPage = 1
    }
    
    private func loadMoreData() {
        guard !isLoading else { return }
        
        isLoading = true
        let allData = data()
        let startIndex = currentPage * pageSize
        let endIndex = min(startIndex + pageSize, allData.count)
        
        if startIndex < allData.count {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                loadedData.append(contentsOf: allData[startIndex..<endIndex])
                currentPage += 1
                isLoading = false
            }
        } else {
            isLoading = false
        }
    }
}

// MARK: - Memory Pressure Handler

class MemoryPressureHandler {
    static let shared = MemoryPressureHandler()
    
    private var source: DispatchSourceMemoryPressure?
    
    private init() {
        setupMemoryPressureSource()
    }
    
    private func setupMemoryPressureSource() {
        source = DispatchSource.makeMemoryPressureSource(eventMask: [.warning, .critical], queue: .main)
        
        source?.setEventHandler { [weak self] in
            self?.handleMemoryPressure()
        }
        
        source?.resume()
    }
    
    private func handleMemoryPressure() {
        // 清理缓存
        ImageCache.shared.clearCache()
        
        // 强制垃圾回收
        autoreleasepool {
            // 清理临时对象
        }
        
        // 发送内存压力通知
        NotificationCenter.default.post(name: .memoryPressure, object: nil)
    }
}

// MARK: - Performance Metrics

struct PerformanceSnapshot {
    let fps: Double
    let memoryUsage: UInt64
    let cpuUsage: Double
    let timestamp: Date
    
    var formattedMemoryUsage: String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(memoryUsage))
    }
    
    var formattedCPUUsage: String {
        return String(format: "%.1f%%", cpuUsage)
    }
    
    var formattedFPS: String {
        return String(format: "%.1f FPS", fps)
    }
}

// MARK: - Performance Dashboard

struct PerformanceDashboard: View {
    @StateObject private var monitor = PerformanceMonitor.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Monitor")
                .font(.headline)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("FPS")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.1f", monitor.currentFPS))
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .leading) {
                    Text("Memory")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(ByteCountFormatter().string(fromByteCount: Int64(monitor.memoryUsage)))
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .leading) {
                    Text("CPU")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.1f%%", monitor.cpuUsage))
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
            
            Toggle("Monitor Performance", isOn: .constant(monitor.isMonitoring))
                .onChange(of: monitor.isMonitoring) { isOn in
                    if isOn {
                        monitor.startMonitoring()
                    } else {
                        monitor.stopMonitoring()
                    }
                }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
        .onAppear {
            monitor.startMonitoring()
        }
        .onDisappear {
            monitor.stopMonitoring()
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let memoryPressure = Notification.Name("memoryPressure")
}

// MARK: - Preview Support

#if DEBUG
struct PerformanceOptimizations_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            PerformanceDashboard()
            
            LazyDataLoader(
                data: { Array(0..<1000).map { $0 as AnyHashable } },
                pageSize: 20
            ) { item in
                Text("Item \(item)")
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
            .frame(height: 300)
        }
        .padding()
        .frame(width: 400, height: 500)
    }
}
#endif