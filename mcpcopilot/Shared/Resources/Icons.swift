//
//  Icons.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Icons and SF Symbols management
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Icon System

/// 统一的图标管理系统 - macOS 优化版本
struct Icons {
    
    // MARK: - System Icons
    
    /// 系统相关图标
    struct System {
        static let settings = "gear"
        static let info = "info.circle"
        static let warning = "exclamationmark.triangle"
        static let error = "xmark.circle"
        static let success = "checkmark.circle"
        static let loading = "arrow.triangle.2.circlepath"
        static let refresh = "arrow.clockwise"
        static let search = "magnifyingglass"
        static let filter = "line.3.horizontal.decrease.circle"
        static let sort = "arrow.up.arrow.down"
        static let close = "xmark"
        static let minimize = "minus"
        static let maximize = "plus"
        static let fullscreen = "arrow.up.left.and.arrow.down.right"
    }
    
    /// 导航相关图标
    struct Navigation {
        static let back = "chevron.left"
        static let forward = "chevron.right"
        static let up = "chevron.up"
        static let down = "chevron.down"
        static let home = "house"
        static let menu = "line.3.horizontal"
        static let sidebar = "sidebar.left"
        static let tab = "rectangle.split.3x1"
        static let window = "macwindow"
    }
    
    /// 文件和文档图标
    struct Files {
        static let folder = "folder"
        static let folderOpen = "folder.fill"
        static let file = "doc"
        static let fileText = "doc.text"
        static let fileCode = "curlybraces"
        static let fileImage = "photo"
        static let fileVideo = "video"
        static let fileAudio = "music.note"
        static let download = "arrow.down.circle"
        static let upload = "arrow.up.circle"
        static let export = "square.and.arrow.up"
        static let import = "square.and.arrow.down"
    }
    
    /// 媒体控制图标
    struct Media {
        static let play = "play.fill"
        static let pause = "pause.fill"
        static let stop = "stop.fill"
        static let previous = "backward.fill"
        static let next = "forward.fill"
        static let volume = "speaker.2"
        static let volumeMute = "speaker.slash"
        static let record = "record.circle"
        static let camera = "camera"
    }
    
    /// 网络和连接图标
    struct Network {
        static let wifi = "wifi"
        static let ethernet = "cable.connector"
        static let cloud = "cloud"
        static let server = "server.rack"
        static let api = "network"
        static let database = "externaldrive.connected.to.line.below"
        static let sync = "arrow.triangle.2.circlepath.circle"
        static let offline = "wifi.slash"
        static let connecting = "dot.radiowaves.left.and.right"
    }
    
    /// 工具和实用程序图标
    struct Tools {
        static let terminal = "terminal"
        static let code = "curlybraces.square"
        static let debug = "ladybug"
        static let performance = "speedometer"
        static let memory = "memorychip"
        static let cpu = "cpu"
        static let disk = "internaldrive"
        static let monitor = "display"
        static let keyboard = "keyboard"
        static let mouse = "computermouse"
    }
    
    /// MCP 特定图标
    struct MCP {
        static let scene = "play.rectangle"
        static let server = "server.rack"
        static let process = "gearshape.2"
        static let log = "text.alignleft"
        static let monitor = "chart.bar.xaxis"
        static let configuration = "slider.horizontal.3"
        static let security = "lock.shield"
        static let cache = "tray.full"
        static let animation = "film"
        static let game = "gamecontroller"
    }
    
    /// 状态指示图标
    struct Status {
        static let running = "play.circle.fill"
        static let stopped = "stop.circle.fill"
        static let starting = "arrow.triangle.2.circlepath.circle"
        static let stopping = "pause.circle.fill"
        static let error = "xmark.circle.fill"
        static let warning = "exclamationmark.triangle.fill"
        static let success = "checkmark.circle.fill"
        static let pending = "clock.circle.fill"
        static let unknown = "questionmark.circle.fill"
    }
}

// MARK: - Icon Extensions

extension Image {
    
    /// 创建系统图标
    static func systemIcon(_ name: String, size: CGFloat = 16) -> Image {
        Image(systemName: name)
            .font(.system(size: size))
    }
    
    /// 创建自定义图标
    static func customIcon(_ name: String, bundle: Bundle = .main) -> Image {
        if let nsImage = bundle.image(forResource: name) {
            return Image(nsImage: nsImage)
        } else {
            return Image(systemName: "questionmark.square.dashed")
        }
    }
    
    /// 创建状态图标
    static func statusIcon(for status: ProcessStatus, size: CGFloat = 16) -> Image {
        let iconName: String
        switch status {
        case .running:
            iconName = Icons.Status.running
        case .stopped:
            iconName = Icons.Status.stopped
        case .starting:
            iconName = Icons.Status.starting
        case .stopping:
            iconName = Icons.Status.stopping
        case .error:
            iconName = Icons.Status.error
        }
        
        return Image(systemName: iconName)
            .font(.system(size: size))
    }
    
    /// 创建日志级别图标
    static func logLevelIcon(for level: LogLevel, size: CGFloat = 14) -> Image {
        let iconName: String
        switch level {
        case .debug:
            iconName = Icons.Tools.debug
        case .info:
            iconName = Icons.System.info
        case .warning:
            iconName = Icons.System.warning
        case .error:
            iconName = Icons.System.error
        }
        
        return Image(systemName: iconName)
            .font(.system(size: size))
    }
}

// MARK: - Icon Button Components

struct IconButton: View {
    let icon: String
    let size: CGFloat
    let color: Color
    let action: () -> Void
    
    init(
        icon: String,
        size: CGFloat = 16,
        color: Color = .primary,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.size = size
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size))
                .foregroundColor(color)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            #if canImport(AppKit)
            if hovering {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
            #endif
        }
    }
}

struct IconToggle: View {
    let iconOn: String
    let iconOff: String
    let size: CGFloat
    let color: Color
    @Binding var isOn: Bool
    
    init(
        iconOn: String,
        iconOff: String,
        size: CGFloat = 16,
        color: Color = .primary,
        isOn: Binding<Bool>
    ) {
        self.iconOn = iconOn
        self.iconOff = iconOff
        self.size = size
        self.color = color
        self._isOn = isOn
    }
    
    var body: some View {
        Button(action: { isOn.toggle() }) {
            Image(systemName: isOn ? iconOn : iconOff)
                .font(.system(size: size))
                .foregroundColor(color)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Icon Collections

extension Icons {
    
    /// 获取状态图标集合
    static var statusIcons: [String: String] {
        return [
            "running": Status.running,
            "stopped": Status.stopped,
            "starting": Status.starting,
            "stopping": Status.stopping,
            "error": Status.error,
            "warning": Status.warning,
            "success": Status.success,
            "pending": Status.pending,
            "unknown": Status.unknown
        ]
    }
    
    /// 获取工具图标集合
    static var toolIcons: [String: String] {
        return [
            "terminal": Tools.terminal,
            "code": Tools.code,
            "debug": Tools.debug,
            "performance": Tools.performance,
            "memory": Tools.memory,
            "cpu": Tools.cpu,
            "disk": Tools.disk,
            "monitor": Tools.monitor
        ]
    }
    
    /// 获取导航图标集合
    static var navigationIcons: [String: String] {
        return [
            "back": Navigation.back,
            "forward": Navigation.forward,
            "home": Navigation.home,
            "menu": Navigation.menu,
            "sidebar": Navigation.sidebar,
            "window": Navigation.window
        ]
    }
}

// MARK: - macOS Specific Icons

#if canImport(AppKit)
extension Icons {
    
    /// macOS 系统图标
    struct MacOS {
        static let finder = "finder"
        static let trash = "trash"
        static let desktop = "desktopcomputer"
        static let dock = "dock.rectangle"
        static let spotlight = "magnifyingglass.circle"
        static let mission_control = "square.grid.3x3"
        static let launchpad = "grid.circle"
        static let notification = "bell"
        static let preferences = "gearshape"
        static let activity_monitor = "speedometer"
    }
    
    /// 窗口控制图标
    struct Window {
        static let close = "xmark.circle"
        static let minimize = "minus.circle"
        static let zoom = "plus.circle"
        static let fullscreen = "arrow.up.left.and.arrow.down.right.circle"
        static let restore = "arrow.down.right.and.arrow.up.left.circle"
    }
    
    /// 菜单图标
    struct Menu {
        static let file = "doc"
        static let edit = "pencil"
        static let view = "eye"
        static let window = "macwindow"
        static let help = "questionmark.circle"
    }
}

extension NSImage {
    
    /// 创建带颜色的图标
    func tinted(with color: NSColor) -> NSImage {
        let image = self.copy() as! NSImage
        image.lockFocus()
        
        color.set()
        let imageRect = NSRect(origin: .zero, size: image.size)
        imageRect.fill(using: .sourceAtop)
        
        image.unlockFocus()
        return image
    }
    
    /// 调整图标大小
    func resized(to size: NSSize) -> NSImage {
        let newImage = NSImage(size: size)
        newImage.lockFocus()
        
        let rect = NSRect(origin: .zero, size: size)
        self.draw(in: rect, from: .zero, operation: .copy, fraction: 1.0)
        
        newImage.unlockFocus()
        return newImage
    }
}
#endif

// MARK: - Preview Support

#if DEBUG
struct Icons_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 系统图标展示
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 16) {
                ForEach(Array(Icons.statusIcons.keys), id: \.self) { key in
                    VStack {
                        Image(systemName: Icons.statusIcons[key]!)
                            .font(.system(size: 24))
                            .foregroundColor(.blue)
                        Text(key)
                            .font(.caption)
                    }
                }
            }
            
            // 交互式图标按钮
            HStack(spacing: 16) {
                IconButton(icon: Icons.System.settings, size: 20, color: .blue) {
                    print("Settings tapped")
                }
                
                IconButton(icon: Icons.Media.play, size: 20, color: .green) {
                    print("Play tapped")
                }
                
                IconButton(icon: Icons.System.error, size: 20, color: .red) {
                    print("Error tapped")
                }
            }
            
            // 切换图标
            HStack {
                @State var isPlaying = false
                IconToggle(
                    iconOn: Icons.Media.pause,
                    iconOff: Icons.Media.play,
                    size: 24,
                    color: .primary,
                    isOn: $isPlaying
                )
            }
        }
        .padding()
        .frame(width: 400, height: 500)
    }
}
#endif