//
//  InteractionStates.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Interaction States and Visual Feedback system
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Interaction State System

/// 统一的交互状态管理系统 - macOS 优化版本
struct InteractionStates {
    
    // MARK: - Basic Interaction States
    
    /// 默认状态 - 元素的初始状态
    static let `default` = InteractionState(
        scale: 1.0,
        opacity: 1.0,
        backgroundColor: DesignTokens.color.backgroundPrimary.value,
        borderColor: DesignTokens.color.borderPrimary.value,
        shadow: Shadows.none,
        cursor: .arrow
    )
    
    /// 悬停状态 - 鼠标悬停时的视觉反馈
    static let hover = InteractionState(
        scale: 1.02,
        opacity: 1.0,
        backgroundColor: DesignTokens.color.backgroundSecondary.value,
        borderColor: DesignTokens.color.borderPrimary.value,
        shadow: Shadows.sm,
        cursor: .pointingHand
    )
    
    /// 按下状态 - 鼠标按下时的视觉反馈
    static let pressed = InteractionState(
        scale: 0.98,
        opacity: 0.9,
        backgroundColor: DesignTokens.color.backgroundTertiary.value,
        borderColor: DesignTokens.color.borderSecondary.value,
        shadow: Shadows.xs,
        cursor: .pointingHand
    )
    
    /// 禁用状态 - 元素不可交互时的状态
    static let disabled = InteractionState(
        scale: 1.0,
        opacity: 0.6,
        backgroundColor: DesignTokens.color.backgroundSecondary.value,
        borderColor: DesignTokens.color.borderSecondary.value,
        shadow: Shadows.none,
        cursor: .arrow
    )
    
    /// 激活状态 - 元素被选中或激活时的状态
    static let active = InteractionState(
        scale: 1.0,
        opacity: 1.0,
        backgroundColor: DesignTokens.color.primary.value.opacity(0.1),
        borderColor: DesignTokens.color.primary.value,
        shadow: Shadows.sm,
        cursor: .arrow
    )
    
    /// 聚焦状态 - 元素获得键盘焦点时的状态
    static let focused = InteractionState(
        scale: 1.0,
        opacity: 1.0,
        backgroundColor: DesignTokens.color.backgroundPrimary.value,
        borderColor: DesignTokens.color.primary.value,
        shadow: Shadows.md,
        cursor: .arrow
    )
    
    // MARK: - Button Interaction States
    
    /// 主要按钮的交互状态
    struct PrimaryButton {
        static let `default` = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.primary.value,
            borderColor: DesignTokens.color.primary.value,
            shadow: Shadows.buttonDefault,
            cursor: .pointingHand
        )
        
        static let hover = InteractionState(
            scale: 1.05,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.primary.value.opacity(0.9),
            borderColor: DesignTokens.color.primary.value,
            shadow: Shadows.buttonHover,
            cursor: .pointingHand
        )
        
        static let pressed = InteractionState(
            scale: 0.95,
            opacity: 0.95,
            backgroundColor: DesignTokens.color.primary.value.opacity(0.8),
            borderColor: DesignTokens.color.primary.value,
            shadow: Shadows.buttonPressed,
            cursor: .pointingHand
        )
        
        static let disabled = InteractionState(
            scale: 1.0,
            opacity: 0.5,
            backgroundColor: DesignTokens.color.backgroundSecondary.value,
            borderColor: DesignTokens.color.borderSecondary.value,
            shadow: Shadows.none,
            cursor: .arrow
        )
    }
    
    /// 次要按钮的交互状态
    struct SecondaryButton {
        static let `default` = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.backgroundSecondary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            shadow: Shadows.buttonDefault,
            cursor: .pointingHand
        )
        
        static let hover = InteractionState(
            scale: 1.02,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.backgroundTertiary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            shadow: Shadows.buttonHover,
            cursor: .pointingHand
        )
        
        static let pressed = InteractionState(
            scale: 0.98,
            opacity: 0.9,
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.borderSecondary.value,
            shadow: Shadows.buttonPressed,
            cursor: .pointingHand
        )
    }
    
    // MARK: - Card Interaction States
    
    /// 卡片的交互状态
    struct Card {
        static let `default` = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            shadow: Shadows.md,
            cursor: .arrow
        )
        
        static let hover = InteractionState(
            scale: 1.02,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            shadow: Shadows.lg,
            cursor: .pointingHand
        )
        
        static let pressed = InteractionState(
            scale: 0.98,
            opacity: 0.95,
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.borderSecondary.value,
            shadow: Shadows.sm,
            cursor: .pointingHand
        )
        
        static let selected = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.surfaceSelection.value,
            borderColor: DesignTokens.color.primary.value,
            shadow: Shadows.lg,
            cursor: .arrow
        )
    }
    
    // MARK: - Input Field States
    
    /// 输入字段的交互状态
    struct InputField {
        static let `default` = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            shadow: Shadows.none,
            cursor: .iBeam
        )
        
        static let focused = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.primary.value,
            shadow: Shadows.sm,
            cursor: .iBeam
        )
        
        static let error = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.error.value.opacity(0.05),
            borderColor: DesignTokens.color.error.value,
            shadow: Shadows.error,
            cursor: .iBeam
        )
        
        static let disabled = InteractionState(
            scale: 1.0,
            opacity: 0.6,
            backgroundColor: DesignTokens.color.backgroundSecondary.value,
            borderColor: DesignTokens.color.borderSecondary.value,
            shadow: Shadows.none,
            cursor: .arrow
        )
    }
    
    // MARK: - List Item States
    
    /// 列表项的交互状态
    struct ListItem {
        static let `default` = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: Color.clear,
            borderColor: Color.clear,
            shadow: Shadows.none,
            cursor: .arrow
        )
        
        static let hover = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.backgroundSecondary.value.opacity(0.5),
            borderColor: Color.clear,
            shadow: Shadows.none,
            cursor: .pointingHand
        )
        
        static let selected = InteractionState(
            scale: 1.0,
            opacity: 1.0,
            backgroundColor: DesignTokens.color.surfaceSelection.value,
            borderColor: Color.clear,
            shadow: Shadows.none,
            cursor: .arrow
        )
        
        static let pressed = InteractionState(
            scale: 0.995,
            opacity: 0.95,
            backgroundColor: DesignTokens.color.backgroundTertiary.value,
            borderColor: Color.clear,
            shadow: Shadows.none,
            cursor: .pointingHand
        )
    }
}

// MARK: - Interaction State Definition

struct InteractionState {
    let scale: CGFloat
    let opacity: Double
    let backgroundColor: Color
    let borderColor: Color
    let shadow: ShadowStyle
    let cursor: CursorStyle
    
    init(
        scale: CGFloat = 1.0,
        opacity: Double = 1.0,
        backgroundColor: Color,
        borderColor: Color,
        shadow: ShadowStyle,
        cursor: CursorStyle = .arrow
    ) {
        self.scale = scale
        self.opacity = opacity
        self.backgroundColor = backgroundColor
        self.borderColor = borderColor
        self.shadow = shadow
        self.cursor = cursor
    }
}

// MARK: - Cursor Style

enum CursorStyle {
    case arrow
    case pointingHand
    case iBeam
    case resizeLeftRight
    case resizeUpDown
    case crosshair
    case openHand
    case closedHand
    
    #if canImport(AppKit)
    var nsCursor: NSCursor {
        switch self {
        case .arrow:
            return .arrow
        case .pointingHand:
            return .pointingHand
        case .iBeam:
            return .iBeam
        case .resizeLeftRight:
            return .resizeLeftRight
        case .resizeUpDown:
            return .resizeUpDown
        case .crosshair:
            return .crosshair
        case .openHand:
            return .openHand
        case .closedHand:
            return .closedHand
        }
    }
    #endif
}

// MARK: - Interactive View Modifier

struct InteractiveModifier: ViewModifier {
    let defaultState: InteractionState
    let hoverState: InteractionState
    let pressedState: InteractionState
    let disabledState: InteractionState?
    let isEnabled: Bool
    let action: (() -> Void)?
    
    @State private var isHovered = false
    @State private var isPressed = false
    
    init(
        defaultState: InteractionState,
        hoverState: InteractionState,
        pressedState: InteractionState,
        disabledState: InteractionState? = nil,
        isEnabled: Bool = true,
        action: (() -> Void)? = nil
    ) {
        self.defaultState = defaultState
        self.hoverState = hoverState
        self.pressedState = pressedState
        self.disabledState = disabledState
        self.isEnabled = isEnabled
        self.action = action
    }
    
    var currentState: InteractionState {
        if !isEnabled, let disabledState = disabledState {
            return disabledState
        } else if isPressed {
            return pressedState
        } else if isHovered {
            return hoverState
        } else {
            return defaultState
        }
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(currentState.scale)
            .opacity(currentState.opacity)
            .background(currentState.backgroundColor)
            .overlay(
                Rectangle()
                    .stroke(currentState.borderColor, lineWidth: 1)
            )
            .shadow(currentState.shadow)
            .animation(DesignTokens.animation.quick.value, value: currentState.scale)
            .animation(DesignTokens.animation.quick.value, value: currentState.opacity)
            .onHover { hovering in
                if isEnabled {
                    withAnimation(DesignTokens.animation.hover.value) {
                        isHovered = hovering
                    }
                    
                    #if canImport(AppKit)
                    if hovering {
                        currentState.cursor.nsCursor.push()
                    } else {
                        NSCursor.pop()
                    }
                    #endif
                }
            }
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        if isEnabled && !isPressed {
                            withAnimation(DesignTokens.animation.quick.value) {
                                isPressed = true
                            }
                        }
                    }
                    .onEnded { _ in
                        if isEnabled && isPressed {
                            withAnimation(DesignTokens.animation.quick.value) {
                                isPressed = false
                            }
                            action?()
                        }
                    }
            )
    }
}

// MARK: - View Extensions

extension View {
    
    /// 应用交互状态
    func interactiveState(
        default defaultState: InteractionState,
        hover hoverState: InteractionState,
        pressed pressedState: InteractionState,
        disabled disabledState: InteractionState? = nil,
        isEnabled: Bool = true,
        action: (() -> Void)? = nil
    ) -> some View {
        self.modifier(InteractiveModifier(
            defaultState: defaultState,
            hoverState: hoverState,
            pressedState: pressedState,
            disabledState: disabledState,
            isEnabled: isEnabled,
            action: action
        ))
    }
    
    /// 应用主要按钮交互状态
    func primaryButtonInteraction(
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> some View {
        self.interactiveState(
            default: InteractionStates.PrimaryButton.default,
            hover: InteractionStates.PrimaryButton.hover,
            pressed: InteractionStates.PrimaryButton.pressed,
            disabled: InteractionStates.PrimaryButton.disabled,
            isEnabled: isEnabled,
            action: action
        )
    }
    
    /// 应用次要按钮交互状态
    func secondaryButtonInteraction(
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> some View {
        self.interactiveState(
            default: InteractionStates.SecondaryButton.default,
            hover: InteractionStates.SecondaryButton.hover,
            pressed: InteractionStates.SecondaryButton.pressed,
            isEnabled: isEnabled,
            action: action
        )
    }
    
    /// 应用卡片交互状态
    func cardInteraction(
        isSelected: Bool = false,
        action: (() -> Void)? = nil
    ) -> some View {
        self.interactiveState(
            default: isSelected ? InteractionStates.Card.selected : InteractionStates.Card.default,
            hover: InteractionStates.Card.hover,
            pressed: InteractionStates.Card.pressed,
            action: action
        )
    }
    
    /// 应用列表项交互状态
    func listItemInteraction(
        isSelected: Bool = false,
        action: (() -> Void)? = nil
    ) -> some View {
        self.interactiveState(
            default: isSelected ? InteractionStates.ListItem.selected : InteractionStates.ListItem.default,
            hover: InteractionStates.ListItem.hover,
            pressed: InteractionStates.ListItem.pressed,
            action: action
        )
    }
    
    /// 应用悬停效果
    func hoverEffect(
        scale: CGFloat = 1.02,
        shadow: ShadowStyle = Shadows.sm
    ) -> some View {
        self.interactiveState(
            default: InteractionStates.default,
            hover: InteractionState(
                scale: scale,
                opacity: 1.0,
                backgroundColor: .clear,
                borderColor: .clear,
                shadow: shadow
            ),
            pressed: InteractionStates.pressed
        )
    }
}

// MARK: - Animation Utilities

extension InteractionState {
    
    /// 创建状态过渡动画
    static func transition(
        from: InteractionState,
        to: InteractionState,
        duration: Double = 0.2
    ) -> Animation {
        return .easeInOut(duration: duration)
    }
}

// MARK: - Accessibility Support

extension InteractiveModifier {
    
    /// 添加无障碍支持
    func accessibilityInteractive(
        label: String,
        hint: String? = nil,
        role: AccessibilityRole = .button
    ) -> some View {
        self
            .accessibilityLabel(label)
            .accessibilityHint(hint ?? "")
            .accessibilityRole(role)
            .accessibilityAddTraits(isEnabled ? [] : .isNotEnabled)
    }
}

// MARK: - Preview Support

#if DEBUG
struct InteractionStates_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            Text("Interaction States Demo")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            // 按钮交互状态
            VStack(spacing: 16) {
                Text("Buttons")
                    .font(.headline)
                
                HStack(spacing: 16) {
                    Text("Primary")
                        .padding()
                        .primaryButtonInteraction {
                            print("Primary button tapped")
                        }
                        .cornerRadius(8)
                    
                    Text("Secondary")
                        .padding()
                        .secondaryButtonInteraction {
                            print("Secondary button tapped")
                        }
                        .cornerRadius(8)
                    
                    Text("Disabled")
                        .padding()
                        .primaryButtonInteraction(isEnabled: false) {
                            print("This shouldn't print")
                        }
                        .cornerRadius(8)
                }
            }
            
            // 卡片交互状态
            VStack(spacing: 16) {
                Text("Cards")
                    .font(.headline)
                
                HStack(spacing: 16) {
                    VStack {
                        Text("Regular Card")
                        Text("Hover to see effect")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .frame(width: 120, height: 80)
                    .cardInteraction {
                        print("Card tapped")
                    }
                    .cornerRadius(8)
                    
                    VStack {
                        Text("Selected Card")
                        Text("Already selected")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .frame(width: 120, height: 80)
                    .cardInteraction(isSelected: true) {
                        print("Selected card tapped")
                    }
                    .cornerRadius(8)
                }
            }
            
            // 列表项交互状态
            VStack(spacing: 16) {
                Text("List Items")
                    .font(.headline)
                
                VStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        HStack {
                            Text("List Item \(index + 1)")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                        .listItemInteraction(isSelected: index == 1) {
                            print("List item \(index + 1) tapped")
                        }
                    }
                }
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .frame(width: 600, height: 700)
    }
}
#endif