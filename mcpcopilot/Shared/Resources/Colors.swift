//
//  Colors.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

extension Color {
    
    // MARK: - Primary Colors
    static let primaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let primaryGreen = Color(red: 0.2, green: 0.78, blue: 0.35)
    static let primaryRed = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let primaryOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let primaryPurple = Color(red: 0.69, green: 0.32, blue: 0.87)
    
    // MARK: - Background Colors
    static let backgroundPrimary = Color(NSColor.controlBackgroundColor)
    static let backgroundSecondary = Color(NSColor.controlColor)
    static let backgroundTertiary = Color(NSColor.tertiarySystemFill)
    
    // MARK: - Text Colors
    static let textPrimary = Color(NSColor.labelColor)
    static let textSecondary = Color(NSColor.secondaryLabelColor)
    static let textTertiary = Color(NSColor.tertiaryLabelColor)
    static let textPlaceholder = Color(NSColor.placeholderTextColor)
    
    // MARK: - Status Colors
    static let statusSuccess = Color.primaryGreen
    static let statusWarning = Color.primaryOrange
    static let statusError = Color.primaryRed
    static let statusInfo = Color.primaryBlue
    
    // MARK: - Border Colors
    static let borderPrimary = Color(NSColor.separatorColor)
    static let borderSecondary = Color(NSColor.gridColor)
    
    // MARK: - Accent Colors
    static let accentBlue = Color.primaryBlue
    static let accentGreen = Color.primaryGreen
    static let accentRed = Color.primaryRed
    
    // MARK: - Process Status Colors
    static let processRunning = Color.primaryGreen
    static let processStopped = Color(NSColor.systemGray)
    static let processStarting = Color.primaryOrange
    static let processStopping = Color.primaryOrange
    static let processError = Color.primaryRed
    
    // MARK: - Chart Colors
    static let chartBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let chartGreen = Color(red: 0.2, green: 0.78, blue: 0.35)
    static let chartOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let chartRed = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let chartPurple = Color(red: 0.69, green: 0.32, blue: 0.87)
    static let chartYellow = Color(red: 1.0, green: 0.8, blue: 0.0)
    
    // MARK: - Sidebar Colors
    static let sidebarBackground = Color(NSColor.controlBackgroundColor)
    static let sidebarSelection = Color(NSColor.selectedControlColor)
    
    // MARK: - Card Colors
    static let cardBackground = Color(NSColor.controlBackgroundColor)
    static let cardBorder = Color(NSColor.separatorColor)
    
    // MARK: - Button Colors
    static let buttonPrimary = Color.primaryBlue
    static let buttonSecondary = Color(NSColor.controlColor)
    static let buttonDanger = Color.primaryRed
    static let buttonSuccess = Color.primaryGreen
}

// MARK: - Color Scheme Support
extension Color {
    
    /// 根据当前颜色方案返回适配的颜色
    static func adaptive(light: Color, dark: Color) -> Color {
        return Color(NSColor.controlAccentColor)
    }
    
    /// 获取状态对应的颜色
    static func forProcessStatus(_ status: ProcessStatus) -> Color {
        switch status {
        case .running:
            return .processRunning
        case .stopped:
            return .processStopped
        case .starting:
            return .processStarting
        case .stopping:
            return .processStopping
        case .error:
            return .processError
        }
    }
    
    /// 获取日志级别对应的颜色
    static func forLogLevel(_ level: LogLevel) -> Color {
        switch level {
        case .debug:
            return .textSecondary
        case .info:
            return .statusInfo
        case .warning:
            return .statusWarning
        case .error:
            return .statusError
        }
    }
}

// MARK: - Gradient Colors
extension LinearGradient {
    
    static let primaryGradient = LinearGradient(
        colors: [Color.primaryBlue, Color.primaryPurple],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let successGradient = LinearGradient(
        colors: [Color.primaryGreen.opacity(0.8), Color.primaryGreen],
        startPoint: .top,
        endPoint: .bottom
    )
    
    static let warningGradient = LinearGradient(
        colors: [Color.primaryOrange.opacity(0.8), Color.primaryOrange],
        startPoint: .top,
        endPoint: .bottom
    )
    
    static let errorGradient = LinearGradient(
        colors: [Color.primaryRed.opacity(0.8), Color.primaryRed],
        startPoint: .top,
        endPoint: .bottom
    )
}

// MARK: - Hex Color Support
extension Color {
    
    /// Initialize color from hex string
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// Convert color to hex string
    var hexString: String {
        #if canImport(AppKit)
        let nsColor = NSColor(self)
        guard let rgbColor = nsColor.usingColorSpace(.sRGB) else {
            return "#000000"
        }
        
        let red = Int(rgbColor.redComponent * 255)
        let green = Int(rgbColor.greenComponent * 255)
        let blue = Int(rgbColor.blueComponent * 255)
        
        return String(format: "#%02X%02X%02X", red, green, blue)
        #else
        return "#000000"
        #endif
    }
    
    /// Create a lighter version of the color
    func lighter(by percentage: CGFloat = 0.2) -> Color {
        #if canImport(AppKit)
        let nsColor = NSColor(self)
        guard let rgbColor = nsColor.usingColorSpace(.sRGB) else {
            return self
        }
        
        let red = min(rgbColor.redComponent + percentage, 1.0)
        let green = min(rgbColor.greenComponent + percentage, 1.0)
        let blue = min(rgbColor.blueComponent + percentage, 1.0)
        
        return Color(.sRGB, red: red, green: green, blue: blue, opacity: rgbColor.alphaComponent)
        #else
        return self
        #endif
    }
    
    /// Create a darker version of the color
    func darker(by percentage: CGFloat = 0.2) -> Color {
        #if canImport(AppKit)
        let nsColor = NSColor(self)
        guard let rgbColor = nsColor.usingColorSpace(.sRGB) else {
            return self
        }
        
        let red = max(rgbColor.redComponent - percentage, 0.0)
        let green = max(rgbColor.greenComponent - percentage, 0.0)
        let blue = max(rgbColor.blueComponent - percentage, 0.0)
        
        return Color(.sRGB, red: red, green: green, blue: blue, opacity: rgbColor.alphaComponent)
        #else
        return self
        #endif
    }
}

// MARK: - macOS System Colors
extension Color {
    
    /// macOS system colors
    static var systemColors: [String: Color] {
        #if canImport(AppKit)
        return [
            "systemRed": Color(NSColor.systemRed),
            "systemOrange": Color(NSColor.systemOrange),
            "systemYellow": Color(NSColor.systemYellow),
            "systemGreen": Color(NSColor.systemGreen),
            "systemMint": Color(NSColor.systemMint),
            "systemTeal": Color(NSColor.systemTeal),
            "systemCyan": Color(NSColor.systemCyan),
            "systemBlue": Color(NSColor.systemBlue),
            "systemIndigo": Color(NSColor.systemIndigo),
            "systemPurple": Color(NSColor.systemPurple),
            "systemPink": Color(NSColor.systemPink),
            "systemBrown": Color(NSColor.systemBrown),
            "systemGray": Color(NSColor.systemGray),
        ]
        #else
        return [:]
        #endif
    }
    
    /// macOS interface colors
    static var interfaceColors: [String: Color] {
        #if canImport(AppKit)
        return [
            "windowBackground": Color(NSColor.windowBackgroundColor),
            "underPageBackground": Color(NSColor.underPageBackgroundColor),
            "controlBackground": Color(NSColor.controlBackgroundColor),
            "selectedControlColor": Color(NSColor.selectedControlColor),
            "selectedContentBackground": Color(NSColor.selectedContentBackgroundColor),
            "unemphasizedSelectedContentBackground": Color(NSColor.unemphasizedSelectedContentBackgroundColor),
            "alternatingContentBackground": Color(NSColor.alternatingContentBackgroundColor),
            "separator": Color(NSColor.separatorColor),
            "gridColor": Color(NSColor.gridColor),
            "headerText": Color(NSColor.headerTextColor),
            "label": Color(NSColor.labelColor),
            "secondaryLabel": Color(NSColor.secondaryLabelColor),
            "tertiaryLabel": Color(NSColor.tertiaryLabelColor),
            "quaternaryLabel": Color(NSColor.quaternaryLabelColor),
        ]
        #else
        return [:]
        #endif
    }
}
