//
//  DesignTokens.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  Adapted from focusflyer with macOS optimizations
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - App Color Scheme Definition

public enum AppColorScheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case auto = "auto"
    
    var displayName: String {
        switch self {
        case .light: return "浅色"
        case .dark: return "深色"
        case .auto: return "自动"
        }
    }
}

// MARK: - Design Tokens Manager

/// 设计令牌管理器 - 统一管理所有设计令牌和主题切换 (macOS optimized)
@MainActor
public class DesignTokens: ObservableObject {
    
    // MARK: - Singleton
    public static let shared = DesignTokens()
    
    // MARK: - Theme Management
    @Published public var currentTheme: AppColorScheme = .auto
    @Published public var currentColorScheme: AppColorScheme = .light
    
    private init() {
        // 从 UserDefaults 加载主题设置
        if let savedTheme = UserDefaults.standard.string(forKey: "app_theme"),
           let theme = AppColorScheme(rawValue: savedTheme) {
            self.currentTheme = theme
        }
        
        // 监听 macOS 系统主题变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(systemThemeChanged),
            name: .init("AppleInterfaceThemeChangedNotification"),
            object: nil
        )
        
        updateColorScheme()
    }
    
    @objc private func systemThemeChanged() {
        if currentTheme == .auto {
            updateColorScheme()
        }
    }
    
    private func updateColorScheme() {
        switch currentTheme {
        case .light:
            currentColorScheme = .light
        case .dark:
            currentColorScheme = .dark
        case .auto:
            // 检测 macOS 系统主题
            #if canImport(AppKit)
            let appearance = NSApp.effectiveAppearance
            if appearance.bestMatch(from: [.darkAqua, .aqua]) == .darkAqua {
                currentColorScheme = .dark
            } else {
                currentColorScheme = .light
            }
            #else
            currentColorScheme = .light
            #endif
        }
    }
    
    // MARK: - Theme Switching
    public func setTheme(_ theme: AppColorScheme) {
        currentTheme = theme
        updateColorScheme()
        
        // 保存到 UserDefaults
        UserDefaults.standard.set(theme.rawValue, forKey: "app_theme")
        
        // 发送通知
        NotificationCenter.default.post(name: .themeChanged, object: nil)
    }
    
    // MARK: - Token Categories
    
    /// 颜色令牌
    public struct ColorTokens {
        // 品牌色
        public static let primary = Token<Color>("color.brand.primary", defaultValue: .primaryBlue)
        public static let secondary = Token<Color>("color.brand.secondary", defaultValue: .textSecondary)
        public static let accent = Token<Color>("color.brand.accent", defaultValue: .primaryOrange)
        
        // 语义色
        public static let success = Token<Color>("color.semantic.success", defaultValue: .primaryGreen)
        public static let warning = Token<Color>("color.semantic.warning", defaultValue: .primaryOrange)
        public static let error = Token<Color>("color.semantic.error", defaultValue: .primaryRed)
        public static let info = Token<Color>("color.semantic.info", defaultValue: .primaryBlue)
        
        // 文本色
        public static let textPrimary = Token<Color>("color.text.primary", defaultValue: .textPrimary)
        public static let textSecondary = Token<Color>("color.text.secondary", defaultValue: .textSecondary)
        public static let textTertiary = Token<Color>("color.text.tertiary", defaultValue: .textTertiary)
        public static let textCode = Token<Color>("color.text.code", defaultValue: .textPrimary)
        
        // 背景色
        public static let backgroundPrimary = Token<Color>("color.background.primary", defaultValue: .backgroundPrimary)
        public static let backgroundSecondary = Token<Color>("color.background.secondary", defaultValue: .backgroundSecondary)
        public static let backgroundTertiary = Token<Color>("color.background.tertiary", defaultValue: .backgroundTertiary)
        public static let backgroundWindow = Token<Color>("color.background.window", defaultValue: .backgroundPrimary)
        
        // 边框色
        public static let borderPrimary = Token<Color>("color.border.primary", defaultValue: .borderPrimary)
        public static let borderSecondary = Token<Color>("color.border.secondary", defaultValue: .borderSecondary)
        public static let borderDivider = Token<Color>("color.border.divider", defaultValue: .borderSecondary)
        
        // macOS 特定色彩
        public static let surfaceSidebar = Token<Color>("color.surface.sidebar", defaultValue: .sidebarBackground)
        public static let surfaceCard = Token<Color>("color.surface.card", defaultValue: .cardBackground)
        public static let surfaceSelection = Token<Color>("color.surface.selection", defaultValue: .sidebarSelection)
    }
    
    /// 间距令牌
    public struct SpacingTokens {
        public static let xs = Token<CGFloat>("spacing.xs", defaultValue: 4)
        public static let sm = Token<CGFloat>("spacing.sm", defaultValue: 8)
        public static let md = Token<CGFloat>("spacing.md", defaultValue: 12)
        public static let lg = Token<CGFloat>("spacing.lg", defaultValue: 16)
        public static let xl = Token<CGFloat>("spacing.xl", defaultValue: 24)
        public static let xxl = Token<CGFloat>("spacing.xxl", defaultValue: 32)
        public static let xxxl = Token<CGFloat>("spacing.xxxl", defaultValue: 48)
        
        // macOS 特定间距
        public static let windowPadding = Token<CGFloat>("spacing.window.padding", defaultValue: 20)
        public static let toolbarHeight = Token<CGFloat>("spacing.toolbar.height", defaultValue: 52)
        public static let sidebarWidth = Token<CGFloat>("spacing.sidebar.width", defaultValue: 280)
        public static let detailViewMinWidth = Token<CGFloat>("spacing.detail.min.width", defaultValue: 400)
        
        // 组件间距
        public static let buttonPadding = Token<EdgeInsets>(
            "spacing.button.padding",
            defaultValue: EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16)
        )
        public static let cardPadding = Token<EdgeInsets>(
            "spacing.card.padding",
            defaultValue: EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
        )
    }
    
    /// 字体令牌
    public struct TypographyTokens {
        public static let largeTitle = Token<Font>("typography.largeTitle", defaultValue: .largeTitle)
        public static let title = Token<Font>("typography.title", defaultValue: .title)
        public static let title2 = Token<Font>("typography.title2", defaultValue: .title2)
        public static let title3 = Token<Font>("typography.title3", defaultValue: .title3)
        public static let headline = Token<Font>("typography.headline", defaultValue: .headline)
        public static let body = Token<Font>("typography.body", defaultValue: .body)
        public static let callout = Token<Font>("typography.callout", defaultValue: .callout)
        public static let subheadline = Token<Font>("typography.subheadline", defaultValue: .subheadline)
        public static let footnote = Token<Font>("typography.footnote", defaultValue: .footnote)
        public static let caption = Token<Font>("typography.caption", defaultValue: .caption)
        public static let caption2 = Token<Font>("typography.caption2", defaultValue: .caption2)
        
        // macOS 特定字体
        public static let code = Token<Font>("typography.code", defaultValue: .system(.body, design: .monospaced))
        public static let menuItem = Token<Font>("typography.menuItem", defaultValue: .system(.body))
        public static let toolbarItem = Token<Font>("typography.toolbarItem", defaultValue: .system(.callout))
    }
    
    /// 阴影令牌
    public struct ShadowTokens {
        public static let none = Token<ShadowConfig>(
            "shadow.none", 
            defaultValue: ShadowConfig(color: .clear, radius: 0, offset: .zero, opacity: 0)
        )
        public static let sm = Token<ShadowConfig>(
            "shadow.sm",
            defaultValue: ShadowConfig(
                color: .black, radius: 2, offset: CGSize(width: 0, height: 1), opacity: 0.1
            )
        )
        public static let md = Token<ShadowConfig>(
            "shadow.md",
            defaultValue: ShadowConfig(
                color: .black, radius: 4, offset: CGSize(width: 0, height: 2), opacity: 0.15
            )
        )
        public static let lg = Token<ShadowConfig>(
            "shadow.lg",
            defaultValue: ShadowConfig(
                color: .black, radius: 8, offset: CGSize(width: 0, height: 4), opacity: 0.2
            )
        )
        public static let xl = Token<ShadowConfig>(
            "shadow.xl",
            defaultValue: ShadowConfig(
                color: .black, radius: 16, offset: CGSize(width: 0, height: 8), opacity: 0.25
            )
        )
        
        // macOS 特定阴影
        public static let window = Token<ShadowConfig>(
            "shadow.window",
            defaultValue: ShadowConfig(
                color: .black, radius: 10, offset: CGSize(width: 0, height: 5), opacity: 0.3
            )
        )
        public static let floatingPanel = Token<ShadowConfig>(
            "shadow.floatingPanel",
            defaultValue: ShadowConfig(
                color: .black, radius: 12, offset: CGSize(width: 0, height: 6), opacity: 0.2
            )
        )
    }
    
    /// 圆角令牌
    public struct BorderRadiusTokens {
        public static let none = Token<CGFloat>("border.radius.none", defaultValue: 0)
        public static let sm = Token<CGFloat>("border.radius.sm", defaultValue: 4)
        public static let md = Token<CGFloat>("border.radius.md", defaultValue: 8)
        public static let lg = Token<CGFloat>("border.radius.lg", defaultValue: 12)
        public static let xl = Token<CGFloat>("border.radius.xl", defaultValue: 16)
        public static let full = Token<CGFloat>("border.radius.full", defaultValue: 9999)
        
        // macOS 特定圆角
        public static let window = Token<CGFloat>("border.radius.window", defaultValue: 10)
        public static let button = Token<CGFloat>("border.radius.button", defaultValue: 6)
        public static let card = Token<CGFloat>("border.radius.card", defaultValue: 8)
    }
    
    /// 动画令牌
    public struct AnimationTokens {
        public static let quick = Token<Animation>("animation.quick", defaultValue: .easeInOut(duration: 0.15))
        public static let standard = Token<Animation>("animation.standard", defaultValue: .easeInOut(duration: 0.25))
        public static let slow = Token<Animation>("animation.slow", defaultValue: .easeInOut(duration: 0.35))
        public static let spring = Token<Animation>("animation.spring", defaultValue: .spring(response: 0.5, dampingFraction: 0.8))
        
        // macOS 特定动画
        public static let windowResize = Token<Animation>("animation.window.resize", defaultValue: .easeInOut(duration: 0.3))
        public static let sidebarToggle = Token<Animation>("animation.sidebar.toggle", defaultValue: .easeInOut(duration: 0.2))
        public static let hover = Token<Animation>("animation.hover", defaultValue: .easeInOut(duration: 0.1))
    }
}

// MARK: - Token Definition

/// 设计令牌定义
public struct Token<T> {
    public let key: String
    public let defaultValue: T
    
    public init(_ key: String, defaultValue: T) {
        self.key = key
        self.defaultValue = defaultValue
    }
    
    /// 获取当前值（支持主题切换）
    public var value: T {
        return getValue()
    }
    
    private func getValue() -> T {
        let tokens = DesignTokens.shared
        let colorScheme = tokens.currentColorScheme
        
        // 根据主题返回不同的值
        switch colorScheme {
        case .light:
            return getLightValue()
        case .dark:
            return getDarkValue()
        case .auto:
            return defaultValue
        }
    }
    
    private func getLightValue() -> T {
        // 优先从配置文件或用户自定义中获取
        if let customValue = getCustomValue(for: .light) {
            return customValue
        }
        return defaultValue
    }
    
    private func getDarkValue() -> T {
        // 优先从配置文件或用户自定义中获取
        if let customValue = getCustomValue(for: .dark) {
            return customValue
        }
        // 如果没有暗色模式的自定义值，使用默认值
        return defaultValue
    }
    
    private func getCustomValue(for colorScheme: AppColorScheme) -> T? {
        // 这里可以从配置文件、远程配置或用户自定义中获取值
        let themeKey = "\(key).\(colorScheme == .light ? "light" : "dark")"
        
        if T.self == Color.self {
            if let hexString = UserDefaults.standard.string(forKey: themeKey) {
                return Color(hex: hexString) as? T
            }
        } else if T.self == CGFloat.self {
            if UserDefaults.standard.object(forKey: themeKey) != nil {
                return CGFloat(UserDefaults.standard.double(forKey: themeKey)) as? T
            }
        }
        
        return nil
    }
}

// MARK: - Shadow Configuration

public struct ShadowConfig {
    public let color: Color
    public let radius: CGFloat
    public let offset: CGSize
    public let opacity: Double
    
    public init(color: Color, radius: CGFloat, offset: CGSize, opacity: Double) {
        self.color = color
        self.radius = radius
        self.offset = offset
        self.opacity = opacity
    }
}

// MARK: - Theme Configuration

/// 主题配置
public struct ThemeConfiguration: Codable {
    public let name: String
    public let colorTokens: [String: String]  // 使用 hex 字符串存储颜色
    public let spacingTokens: [String: CGFloat]
    public let borderRadiusTokens: [String: CGFloat]
    
    public init(name: String, colorTokens: [String: String], spacingTokens: [String: CGFloat] = [:], borderRadiusTokens: [String: CGFloat] = [:]) {
        self.name = name
        self.colorTokens = colorTokens
        self.spacingTokens = spacingTokens
        self.borderRadiusTokens = borderRadiusTokens
    }
}

// MARK: - Predefined Themes

extension ThemeConfiguration {
    /// 默认亮色主题
    public static let lightTheme = ThemeConfiguration(
        name: "Light",
        colorTokens: [
            "color.brand.primary": "#007AFF",
            "color.brand.secondary": "#8E8E93",
            "color.brand.accent": "#FF9500",
            "color.text.primary": "#000000",
            "color.text.secondary": "#8E8E93",
            "color.background.primary": "#FFFFFF",
            "color.background.secondary": "#F2F2F7",
        ]
    )
    
    /// 默认暗色主题
    public static let darkTheme = ThemeConfiguration(
        name: "Dark",
        colorTokens: [
            "color.brand.primary": "#0A84FF",
            "color.brand.secondary": "#8E8E93",
            "color.brand.accent": "#FF9F0A",
            "color.text.primary": "#FFFFFF",
            "color.text.secondary": "#8E8E93",
            "color.background.primary": "#000000",
            "color.background.secondary": "#1C1C1E",
        ]
    )
    
    /// MCP 主题
    public static let mcpTheme = ThemeConfiguration(
        name: "MCP",
        colorTokens: [
            "color.brand.primary": "#0066CC",
            "color.brand.secondary": "#666666",
            "color.brand.accent": "#FF6600",
            "color.text.primary": "#333333",
            "color.text.secondary": "#666666",
            "color.background.primary": "#F8F9FA",
            "color.background.secondary": "#E9ECEF",
        ]
    )
}

// MARK: - SwiftUI Extensions

extension View {
    /// 应用当前主题
    public func themedAppearance() -> some View {
        self.environment(\.colorScheme, DesignTokens.shared.currentColorScheme == .light ? .light : .dark)
    }
    
    /// 监听主题变化
    public func onThemeChange(_ action: @escaping (ThemeConfiguration) -> Void) -> some View {
        self.onReceive(NotificationCenter.default.publisher(for: .themeChanged)) { notification in
            if let theme = notification.object as? ThemeConfiguration {
                action(theme)
            }
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let themeChanged = Notification.Name("themeChanged")
}

// MARK: - Token Convenience Access

extension DesignTokens {
    /// 便捷访问器
    public static var color: ColorTokens.Type { ColorTokens.self }
    public static var spacing: SpacingTokens.Type { SpacingTokens.self }
    public static var typography: TypographyTokens.Type { TypographyTokens.self }
    public static var shadow: ShadowTokens.Type { ShadowTokens.self }
    public static var borderRadius: BorderRadiusTokens.Type { BorderRadiusTokens.self }
    public static var animation: AnimationTokens.Type { AnimationTokens.self }
}

// MARK: - Preview Support

#if DEBUG
extension DesignTokens {
    /// 预览模式设置
    public static func setupForPreview(theme: AppColorScheme = .light) {
        Task { @MainActor in
            shared.currentTheme = theme
            shared.updateColorScheme()
        }
    }
}
#endif