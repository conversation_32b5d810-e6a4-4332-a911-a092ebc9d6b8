//
//  Components.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Component Styles and Presets
//

import SwiftUI

// MARK: - Component Style System

/// 统一的组件样式管理系统 - macOS 优化版本
struct ComponentStyles {
    
    // MARK: - Button Styles
    
    /// 按钮样式预设
    struct Button {
        
        /// 主要按钮样式
        static let primary = ButtonStyleConfig(
            backgroundColor: DesignTokens.color.primary.value,
            foregroundColor: .white,
            cornerRadius: DesignTokens.borderRadius.button.value,
            padding: DesignTokens.spacing.buttonPadding.value,
            shadow: Shadows.buttonDefault,
            font: DesignTokens.typography.body.value
        )
        
        /// 次要按钮样式
        static let secondary = ButtonStyleConfig(
            backgroundColor: DesignTokens.color.backgroundSecondary.value,
            foregroundColor: DesignTokens.color.textPrimary.value,
            cornerRadius: DesignTokens.borderRadius.button.value,
            padding: DesignTokens.spacing.buttonPadding.value,
            shadow: Shadows.buttonDefault,
            font: DesignTokens.typography.body.value
        )
        
        /// 危险按钮样式
        static let danger = ButtonStyleConfig(
            backgroundColor: DesignTokens.color.error.value,
            foregroundColor: .white,
            cornerRadius: DesignTokens.borderRadius.button.value,
            padding: DesignTokens.spacing.buttonPadding.value,
            shadow: Shadows.buttonDefault,
            font: DesignTokens.typography.body.value
        )
        
        /// 成功按钮样式
        static let success = ButtonStyleConfig(
            backgroundColor: DesignTokens.color.success.value,
            foregroundColor: .white,
            cornerRadius: DesignTokens.borderRadius.button.value,
            padding: DesignTokens.spacing.buttonPadding.value,
            shadow: Shadows.buttonDefault,
            font: DesignTokens.typography.body.value
        )
        
        /// 文本按钮样式
        static let text = ButtonStyleConfig(
            backgroundColor: .clear,
            foregroundColor: DesignTokens.color.primary.value,
            cornerRadius: DesignTokens.borderRadius.button.value,
            padding: EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12),
            shadow: Shadows.none,
            font: DesignTokens.typography.body.value
        )
        
        /// 图标按钮样式
        static let icon = ButtonStyleConfig(
            backgroundColor: .clear,
            foregroundColor: DesignTokens.color.textSecondary.value,
            cornerRadius: DesignTokens.borderRadius.sm.value,
            padding: EdgeInsets(top: 8, leading: 8, bottom: 8, trailing: 8),
            shadow: Shadows.none,
            font: DesignTokens.typography.body.value
        )
    }
    
    // MARK: - Card Styles
    
    /// 卡片样式预设
    struct Card {
        
        /// 标准卡片样式
        static let standard = CardStyleConfig(
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            cornerRadius: DesignTokens.borderRadius.card.value,
            padding: DesignTokens.spacing.cardPadding.value,
            shadow: Shadows.md,
            borderWidth: 1
        )
        
        /// 强调卡片样式
        static let emphasized = CardStyleConfig(
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.primary.value,
            cornerRadius: DesignTokens.borderRadius.card.value,
            padding: DesignTokens.spacing.cardPadding.value,
            shadow: Shadows.lg,
            borderWidth: 2
        )
        
        /// 浮动卡片样式
        static let floating = CardStyleConfig(
            backgroundColor: DesignTokens.color.surfaceFloating.value,
            borderColor: .clear,
            cornerRadius: DesignTokens.borderRadius.lg.value,
            padding: DesignTokens.spacing.cardPadding.value,
            shadow: Shadows.floatingPanel,
            borderWidth: 0
        )
        
        /// 紧凑卡片样式
        static let compact = CardStyleConfig(
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            cornerRadius: DesignTokens.borderRadius.sm.value,
            padding: EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12),
            shadow: Shadows.sm,
            borderWidth: 1
        )
        
        /// 无边框卡片样式
        static let borderless = CardStyleConfig(
            backgroundColor: DesignTokens.color.surfaceCard.value,
            borderColor: .clear,
            cornerRadius: DesignTokens.borderRadius.card.value,
            padding: DesignTokens.spacing.cardPadding.value,
            shadow: Shadows.md,
            borderWidth: 0
        )
    }
    
    // MARK: - Input Field Styles
    
    /// 输入字段样式预设
    struct InputField {
        
        /// 标准输入框样式
        static let standard = InputFieldStyleConfig(
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            focusedBorderColor: DesignTokens.color.primary.value,
            errorBorderColor: DesignTokens.color.error.value,
            textColor: DesignTokens.color.textPrimary.value,
            placeholderColor: DesignTokens.color.textTertiary.value,
            cornerRadius: DesignTokens.borderRadius.sm.value,
            padding: EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12),
            font: DesignTokens.typography.body.value,
            borderWidth: 1
        )
        
        /// 大输入框样式
        static let large = InputFieldStyleConfig(
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            focusedBorderColor: DesignTokens.color.primary.value,
            errorBorderColor: DesignTokens.color.error.value,
            textColor: DesignTokens.color.textPrimary.value,
            placeholderColor: DesignTokens.color.textTertiary.value,
            cornerRadius: DesignTokens.borderRadius.md.value,
            padding: EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16),
            font: DesignTokens.typography.headline.value,
            borderWidth: 1
        )
        
        /// 搜索框样式
        static let search = InputFieldStyleConfig(
            backgroundColor: DesignTokens.color.backgroundSecondary.value,
            borderColor: .clear,
            focusedBorderColor: DesignTokens.color.primary.value,
            errorBorderColor: DesignTokens.color.error.value,
            textColor: DesignTokens.color.textPrimary.value,
            placeholderColor: DesignTokens.color.textTertiary.value,
            cornerRadius: DesignTokens.borderRadius.full.value,
            padding: EdgeInsets(top: 8, leading: 32, bottom: 8, trailing: 12),
            font: DesignTokens.typography.body.value,
            borderWidth: 0
        )
    }
    
    // MARK: - List Styles
    
    /// 列表样式预设
    struct List {
        
        /// 标准列表样式
        static let standard = ListStyleConfig(
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            separatorColor: DesignTokens.color.borderDivider.value,
            selectionColor: DesignTokens.color.surfaceSelection.value,
            hoverColor: DesignTokens.color.backgroundSecondary.value.opacity(0.5),
            cornerRadius: DesignTokens.borderRadius.md.value,
            itemPadding: EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12),
            showSeparators: true
        )
        
        /// 侧边栏列表样式
        static let sidebar = ListStyleConfig(
            backgroundColor: DesignTokens.color.surfaceSidebar.value,
            separatorColor: .clear,
            selectionColor: DesignTokens.color.surfaceSelection.value,
            hoverColor: DesignTokens.color.backgroundSecondary.value.opacity(0.3),
            cornerRadius: DesignTokens.borderRadius.sm.value,
            itemPadding: EdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12),
            showSeparators: false
        )
        
        /// 卡片列表样式
        static let card = ListStyleConfig(
            backgroundColor: .clear,
            separatorColor: .clear,
            selectionColor: DesignTokens.color.surfaceSelection.value,
            hoverColor: DesignTokens.color.backgroundSecondary.value.opacity(0.5),
            cornerRadius: DesignTokens.borderRadius.card.value,
            itemPadding: EdgeInsets(top: 0, leading: 0, bottom: 12, trailing: 0),
            showSeparators: false
        )
    }
    
    // MARK: - Panel Styles
    
    /// 面板样式预设
    struct Panel {
        
        /// 工具栏面板样式
        static let toolbar = PanelStyleConfig(
            backgroundColor: DesignTokens.color.surfaceToolbar.value,
            borderColor: DesignTokens.color.borderDivider.value,
            cornerRadius: 0,
            padding: EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16),
            shadow: Shadows.toolbar,
            material: Materials.toolbar
        )
        
        /// 侧边栏面板样式
        static let sidebar = PanelStyleConfig(
            backgroundColor: DesignTokens.color.surfaceSidebar.value,
            borderColor: DesignTokens.color.borderDivider.value,
            cornerRadius: 0,
            padding: EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 0),
            shadow: Shadows.sidebar,
            material: Materials.sidebar
        )
        
        /// 浮动面板样式
        static let floating = PanelStyleConfig(
            backgroundColor: DesignTokens.color.surfaceFloating.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            cornerRadius: DesignTokens.borderRadius.lg.value,
            padding: EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16),
            shadow: Shadows.floatingPanel,
            material: Materials.floatingPanel
        )
        
        /// 模态面板样式
        static let modal = PanelStyleConfig(
            backgroundColor: DesignTokens.color.backgroundPrimary.value,
            borderColor: DesignTokens.color.borderPrimary.value,
            cornerRadius: DesignTokens.borderRadius.window.value,
            padding: EdgeInsets(top: 24, leading: 24, bottom: 24, trailing: 24),
            shadow: Shadows.window,
            material: Materials.popover
        )
    }
}

// MARK: - Style Configuration Structs

struct ButtonStyleConfig {
    let backgroundColor: Color
    let foregroundColor: Color
    let cornerRadius: CGFloat
    let padding: EdgeInsets
    let shadow: ShadowStyle
    let font: Font
}

struct CardStyleConfig {
    let backgroundColor: Color
    let borderColor: Color
    let cornerRadius: CGFloat
    let padding: EdgeInsets
    let shadow: ShadowStyle
    let borderWidth: CGFloat
}

struct InputFieldStyleConfig {
    let backgroundColor: Color
    let borderColor: Color
    let focusedBorderColor: Color
    let errorBorderColor: Color
    let textColor: Color
    let placeholderColor: Color
    let cornerRadius: CGFloat
    let padding: EdgeInsets
    let font: Font
    let borderWidth: CGFloat
}

struct ListStyleConfig {
    let backgroundColor: Color
    let separatorColor: Color
    let selectionColor: Color
    let hoverColor: Color
    let cornerRadius: CGFloat
    let itemPadding: EdgeInsets
    let showSeparators: Bool
}

struct PanelStyleConfig {
    let backgroundColor: Color
    let borderColor: Color
    let cornerRadius: CGFloat
    let padding: EdgeInsets
    let shadow: ShadowStyle
    let material: MaterialStyle
}

// MARK: - Style Application Extensions

extension View {
    
    /// 应用按钮样式
    func buttonStyle(_ config: ButtonStyleConfig) -> some View {
        self
            .font(config.font)
            .foregroundColor(config.foregroundColor)
            .padding(config.padding)
            .background(config.backgroundColor)
            .cornerRadius(config.cornerRadius)
            .shadow(config.shadow)
    }
    
    /// 应用卡片样式
    func cardStyle(_ config: CardStyleConfig) -> some View {
        self
            .padding(config.padding)
            .background(config.backgroundColor)
            .cornerRadius(config.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: config.cornerRadius)
                    .stroke(config.borderColor, lineWidth: config.borderWidth)
            )
            .shadow(config.shadow)
    }
    
    /// 应用面板样式
    func panelStyle(_ config: PanelStyleConfig) -> some View {
        self
            .padding(config.padding)
            .background(config.backgroundColor)
            .cornerRadius(config.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: config.cornerRadius)
                    .stroke(config.borderColor, lineWidth: 1)
            )
            .shadow(config.shadow)
            .material(config.material)
    }
}

// MARK: - Custom Button Styles

struct MCPPrimaryButtonStyle: ButtonStyle {
    let config: ButtonStyleConfig
    
    init(_ config: ButtonStyleConfig = ComponentStyles.Button.primary) {
        self.config = config
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .buttonStyle(config)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(DesignTokens.animation.quick.value, value: configuration.isPressed)
    }
}

struct MCPSecondaryButtonStyle: ButtonStyle {
    let config: ButtonStyleConfig
    
    init(_ config: ButtonStyleConfig = ComponentStyles.Button.secondary) {
        self.config = config
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .buttonStyle(config)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(DesignTokens.animation.quick.value, value: configuration.isPressed)
    }
}

// MARK: - Component Presets

extension ComponentStyles {
    
    /// 获取所有按钮样式预设
    static var buttonStyles: [String: ButtonStyleConfig] {
        return [
            "primary": Button.primary,
            "secondary": Button.secondary,
            "danger": Button.danger,
            "success": Button.success,
            "text": Button.text,
            "icon": Button.icon
        ]
    }
    
    /// 获取所有卡片样式预设
    static var cardStyles: [String: CardStyleConfig] {
        return [
            "standard": Card.standard,
            "emphasized": Card.emphasized,
            "floating": Card.floating,
            "compact": Card.compact,
            "borderless": Card.borderless
        ]
    }
    
    /// 获取所有输入框样式预设
    static var inputFieldStyles: [String: InputFieldStyleConfig] {
        return [
            "standard": InputField.standard,
            "large": InputField.large,
            "search": InputField.search
        ]
    }
    
    /// 获取所有面板样式预设
    static var panelStyles: [String: PanelStyleConfig] {
        return [
            "toolbar": Panel.toolbar,
            "sidebar": Panel.sidebar,
            "floating": Panel.floating,
            "modal": Panel.modal
        ]
    }
}

// MARK: - Theme-Aware Components

struct ThemeAwareButton: View {
    let title: String
    let icon: String?
    let style: ButtonStyleConfig
    let action: () -> Void
    
    @ObservedObject private var designTokens = DesignTokens.shared
    
    init(
        title: String,
        icon: String? = nil,
        style: ButtonStyleConfig = ComponentStyles.Button.primary,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 14))
                }
                Text(title)
            }
        }
        .buttonStyle(style)
        .onReceive(NotificationCenter.default.publisher(for: .themeChanged)) { _ in
            // 响应主题变化
        }
    }
}

struct ThemeAwareCard<Content: View>: View {
    let content: Content
    let style: CardStyleConfig
    
    @ObservedObject private var designTokens = DesignTokens.shared
    
    init(
        style: CardStyleConfig = ComponentStyles.Card.standard,
        @ViewBuilder content: () -> Content
    ) {
        self.style = style
        self.content = content()
    }
    
    var body: some View {
        content
            .cardStyle(style)
            .onReceive(NotificationCenter.default.publisher(for: .themeChanged)) { _ in
                // 响应主题变化
            }
    }
}

// MARK: - Responsive Components

struct ResponsiveCard<Content: View>: View {
    let content: Content
    let compactStyle: CardStyleConfig
    let regularStyle: CardStyleConfig
    
    @State private var isCompact = false
    
    init(
        compactStyle: CardStyleConfig = ComponentStyles.Card.compact,
        regularStyle: CardStyleConfig = ComponentStyles.Card.standard,
        @ViewBuilder content: () -> Content
    ) {
        self.compactStyle = compactStyle
        self.regularStyle = regularStyle
        self.content = content()
    }
    
    var body: some View {
        GeometryReader { geometry in
            content
                .cardStyle(geometry.size.width < 300 ? compactStyle : regularStyle)
                .onAppear {
                    isCompact = geometry.size.width < 300
                }
                .onChange(of: geometry.size.width) { width in
                    withAnimation(DesignTokens.animation.standard.value) {
                        isCompact = width < 300
                    }
                }
        }
    }
}

// MARK: - Preview Support

#if DEBUG
struct ComponentStyles_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(spacing: 30) {
                // 按钮样式展示
                VStack(alignment: .leading, spacing: 16) {
                    Text("Button Styles")
                        .font(.headline)
                    
                    HStack(spacing: 12) {
                        ThemeAwareButton(title: "Primary", style: ComponentStyles.Button.primary) {
                            print("Primary tapped")
                        }
                        
                        ThemeAwareButton(title: "Secondary", style: ComponentStyles.Button.secondary) {
                            print("Secondary tapped")
                        }
                        
                        ThemeAwareButton(title: "Danger", style: ComponentStyles.Button.danger) {
                            print("Danger tapped")
                        }
                    }
                    
                    HStack(spacing: 12) {
                        ThemeAwareButton(
                            title: "With Icon",
                            icon: "gear",
                            style: ComponentStyles.Button.primary
                        ) {
                            print("Icon button tapped")
                        }
                        
                        ThemeAwareButton(title: "Text Button", style: ComponentStyles.Button.text) {
                            print("Text button tapped")
                        }
                    }
                }
                
                // 卡片样式展示
                VStack(alignment: .leading, spacing: 16) {
                    Text("Card Styles")
                        .font(.headline)
                    
                    HStack(spacing: 12) {
                        ThemeAwareCard(style: ComponentStyles.Card.standard) {
                            VStack {
                                Text("Standard Card")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                Text("This is a standard card with border and shadow")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .frame(width: 140, height: 100)
                        
                        ThemeAwareCard(style: ComponentStyles.Card.emphasized) {
                            VStack {
                                Text("Emphasized")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                Text("Card with primary border")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .frame(width: 140, height: 100)
                    }
                    
                    ThemeAwareCard(style: ComponentStyles.Card.floating) {
                        VStack {
                            Text("Floating Card")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text("Card with enhanced shadow and no border, perfect for overlays")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(height: 80)
                }
                
                // 面板样式展示
                VStack(alignment: .leading, spacing: 16) {
                    Text("Panel Styles")
                        .font(.headline)
                    
                    VStack {
                        Text("Toolbar Panel")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Text("Used for application toolbars")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .panelStyle(ComponentStyles.Panel.toolbar)
                    .frame(height: 60)
                    
                    VStack {
                        Text("Floating Panel")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Text("Used for dialogs and modal content")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .panelStyle(ComponentStyles.Panel.floating)
                    .frame(height: 80)
                }
            }
            .padding()
        }
        .frame(width: 500, height: 800)
    }
}
#endif