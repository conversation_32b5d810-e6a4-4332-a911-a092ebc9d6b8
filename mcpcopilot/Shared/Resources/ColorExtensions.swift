//
//  ColorExtensions.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//

import SwiftUI

// MARK: - Color Extensions for Status and Log Levels
// This file provides color functions that depend on types defined in DataModels.swift

extension Color {
    
    /// 获取状态对应的颜色
    static func forProcessStatus(_ status: ProcessStatus) -> Color {
        switch status {
        case .running:
            return .processRunning
        case .stopped:
            return .processStopped
        case .starting:
            return .processStarting
        case .stopping:
            return .processStopping
        case .error:
            return .processError
        }
    }
    
    /// 获取日志级别对应的颜色
    static func forLogLevel(_ level: LogLevel) -> Color {
        switch level {
        case .debug:
            return .textSecondary
        case .info:
            return .statusInfo
        case .warning:
            return .statusWarning
        case .error:
            return .statusError
        }
    }
}
