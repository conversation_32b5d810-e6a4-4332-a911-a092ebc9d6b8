//
//  Shadows.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Shadow and Elevation system
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Shadow System

/// 统一的阴影管理系统 - macOS 优化版本
struct Shadows {
    
    // MARK: - Basic Shadows
    
    /// 无阴影
    static let none = ShadowStyle(
        color: .clear,
        radius: 0,
        offset: .zero,
        opacity: 0
    )
    
    /// 极小阴影 - 适用于轻微的分层
    static let xs = ShadowStyle(
        color: .black,
        radius: 1,
        offset: CGSize(width: 0, height: 0.5),
        opacity: 0.1
    )
    
    /// 小阴影 - 适用于按钮悬停状态
    static let sm = ShadowStyle(
        color: .black,
        radius: 2,
        offset: CGSize(width: 0, height: 1),
        opacity: 0.1
    )
    
    /// 中等阴影 - 适用于卡片和面板
    static let md = ShadowStyle(
        color: .black,
        radius: 4,
        offset: CGSize(width: 0, height: 2),
        opacity: 0.15
    )
    
    /// 大阴影 - 适用于模态框和浮动面板
    static let lg = ShadowStyle(
        color: .black,
        radius: 8,
        offset: CGSize(width: 0, height: 4),
        opacity: 0.2
    )
    
    /// 特大阴影 - 适用于全屏模态或重要的浮动内容
    static let xl = ShadowStyle(
        color: .black,
        radius: 16,
        offset: CGSize(width: 0, height: 8),
        opacity: 0.25
    )
    
    /// 超大阴影 - 适用于拖拽状态或最高层级的内容
    static let xxl = ShadowStyle(
        color: .black,
        radius: 24,
        offset: CGSize(width: 0, height: 12),
        opacity: 0.3
    )
    
    // MARK: - macOS Specific Shadows
    
    /// 窗口阴影 - 模拟 macOS 窗口的自然阴影
    static let window = ShadowStyle(
        color: .black,
        radius: 10,
        offset: CGSize(width: 0, height: 5),
        opacity: 0.3
    )
    
    /// 菜单阴影 - 适用于下拉菜单和弹出菜单
    static let menu = ShadowStyle(
        color: .black,
        radius: 6,
        offset: CGSize(width: 0, height: 3),
        opacity: 0.2
    )
    
    /// 工具栏阴影 - 适用于工具栏和导航栏
    static let toolbar = ShadowStyle(
        color: .black,
        radius: 1,
        offset: CGSize(width: 0, height: 1),
        opacity: 0.1
    )
    
    /// 侧边栏阴影 - 适用于侧边栏边缘
    static let sidebar = ShadowStyle(
        color: .black,
        radius: 2,
        offset: CGSize(width: 1, height: 0),
        opacity: 0.08
    )
    
    /// 浮动面板阴影 - 适用于调试面板等浮动元素
    static let floatingPanel = ShadowStyle(
        color: .black,
        radius: 12,
        offset: CGSize(width: 0, height: 6),
        opacity: 0.2
    )
    
    /// 通知阴影 - 适用于通知和提示
    static let notification = ShadowStyle(
        color: .black,
        radius: 8,
        offset: CGSize(width: 0, height: 4),
        opacity: 0.15
    )
    
    // MARK: - Interactive Shadows
    
    /// 按钮默认阴影
    static let buttonDefault = ShadowStyle(
        color: .black,
        radius: 1,
        offset: CGSize(width: 0, height: 1),
        opacity: 0.1
    )
    
    /// 按钮悬停阴影
    static let buttonHover = ShadowStyle(
        color: .black,
        radius: 3,
        offset: CGSize(width: 0, height: 2),
        opacity: 0.15
    )
    
    /// 按钮按下阴影
    static let buttonPressed = ShadowStyle(
        color: .black,
        radius: 1,
        offset: CGSize(width: 0, height: 0.5),
        opacity: 0.08
    )
    
    /// 拖拽阴影
    static let dragging = ShadowStyle(
        color: .black,
        radius: 20,
        offset: CGSize(width: 0, height: 10),
        opacity: 0.3
    )
    
    // MARK: - Colored Shadows
    
    /// 品牌色阴影
    static let brand = ShadowStyle(
        color: DesignTokens.color.primary.value,
        radius: 8,
        offset: CGSize(width: 0, height: 4),
        opacity: 0.2
    )
    
    /// 错误阴影
    static let error = ShadowStyle(
        color: DesignTokens.color.error.value,
        radius: 6,
        offset: CGSize(width: 0, height: 3),
        opacity: 0.2
    )
    
    /// 成功阴影
    static let success = ShadowStyle(
        color: DesignTokens.color.success.value,
        radius: 6,
        offset: CGSize(width: 0, height: 3),
        opacity: 0.2
    )
    
    /// 警告阴影
    static let warning = ShadowStyle(
        color: DesignTokens.color.warning.value,
        radius: 6,
        offset: CGSize(width: 0, height: 3),
        opacity: 0.2
    )
}

// MARK: - Shadow Style Definition

struct ShadowStyle {
    let color: Color
    let radius: CGFloat
    let offset: CGSize
    let opacity: Double
    
    init(color: Color, radius: CGFloat, offset: CGSize, opacity: Double) {
        self.color = color
        self.radius = radius
        self.offset = offset
        self.opacity = opacity
    }
}

// MARK: - Shadow Extensions

extension View {
    
    /// 应用阴影样式
    func shadow(_ style: ShadowStyle) -> some View {
        self.shadow(
            color: style.color.opacity(style.opacity),
            radius: style.radius,
            x: style.offset.width,
            y: style.offset.height
        )
    }
    
    /// 应用多层阴影（iOS 14+）
    @available(macOS 11.0, *)
    func shadows(_ styles: [ShadowStyle]) -> some View {
        var view = AnyView(self)
        
        for style in styles {
            view = AnyView(
                view.shadow(
                    color: style.color.opacity(style.opacity),
                    radius: style.radius,
                    x: style.offset.width,
                    y: style.offset.height
                )
            )
        }
        
        return view
    }
    
    /// 应用交互式阴影
    func interactiveShadow(
        default defaultShadow: ShadowStyle = Shadows.buttonDefault,
        hover hoverShadow: ShadowStyle = Shadows.buttonHover,
        pressed pressedShadow: ShadowStyle = Shadows.buttonPressed
    ) -> some View {
        self.modifier(InteractiveShadowModifier(
            defaultShadow: defaultShadow,
            hoverShadow: hoverShadow,
            pressedShadow: pressedShadow
        ))
    }
    
    /// 应用卡片阴影
    func cardShadow() -> some View {
        self.shadow(Shadows.md)
    }
    
    /// 应用浮动阴影
    func floatingShadow() -> some View {
        self.shadow(Shadows.floatingPanel)
    }
    
    /// 应用窗口阴影
    func windowShadow() -> some View {
        self.shadow(Shadows.window)
    }
}

// MARK: - Interactive Shadow Modifier

struct InteractiveShadowModifier: ViewModifier {
    let defaultShadow: ShadowStyle
    let hoverShadow: ShadowStyle
    let pressedShadow: ShadowStyle
    
    @State private var isHovered = false
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .shadow(currentShadow)
            .onHover { hovering in
                withAnimation(DesignTokens.animation.quick.value) {
                    isHovered = hovering
                }
            }
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(DesignTokens.animation.quick.value, value: isPressed)
    }
    
    private var currentShadow: ShadowStyle {
        if isPressed {
            return pressedShadow
        } else if isHovered {
            return hoverShadow
        } else {
            return defaultShadow
        }
    }
}

// MARK: - Elevation System

/// 材质设计风格的海拔系统
struct Elevation {
    
    /// 海拔级别枚举
    enum Level: Int, CaseIterable {
        case surface = 0
        case level1 = 1
        case level2 = 2
        case level3 = 3
        case level4 = 4
        case level5 = 5
        
        var shadow: ShadowStyle {
            switch self {
            case .surface:
                return Shadows.none
            case .level1:
                return Shadows.xs
            case .level2:
                return Shadows.sm
            case .level3:
                return Shadows.md
            case .level4:
                return Shadows.lg
            case .level5:
                return Shadows.xl
            }
        }
        
        var backgroundColor: Color {
            let opacity = Double(rawValue) * 0.02
            return DesignTokens.color.backgroundPrimary.value
                .opacity(1.0 + opacity)
        }
    }
    
    static func shadow(for level: Level) -> ShadowStyle {
        return level.shadow
    }
    
    static func backgroundColor(for level: Level) -> Color {
        return level.backgroundColor
    }
}

extension View {
    
    /// 应用海拔样式
    func elevation(_ level: Elevation.Level) -> some View {
        self
            .background(Elevation.backgroundColor(for: level))
            .shadow(Elevation.shadow(for: level))
    }
}

// MARK: - macOS Native Shadow Effects

#if canImport(AppKit)
struct NativeShadowModifier: ViewModifier {
    let shadow: NSShadow
    
    func body(content: Content) -> some View {
        content
            .background(
                NativeShadowView(shadow: shadow)
            )
    }
}

struct NativeShadowView: NSViewRepresentable {
    let shadow: NSShadow
    
    func makeNSView(context: Context) -> NSView {
        let view = NSView()
        view.wantsLayer = true
        view.shadow = shadow
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        nsView.shadow = shadow
    }
}

extension View {
    
    /// 应用原生 macOS 阴影
    func nativeShadow(_ shadow: NSShadow) -> some View {
        self.modifier(NativeShadowModifier(shadow: shadow))
    }
    
    /// 应用原生 macOS 窗口阴影
    func nativeWindowShadow() -> some View {
        let shadow = NSShadow()
        shadow.shadowColor = NSColor.shadowColor
        shadow.shadowOffset = NSSize(width: 0, height: -3)
        shadow.shadowBlurRadius = 10
        
        return self.nativeShadow(shadow)
    }
}

extension NSShadow {
    
    /// 创建自定义阴影
    static func custom(
        color: NSColor = .shadowColor,
        offset: NSSize = NSSize(width: 0, height: -2),
        blurRadius: CGFloat = 4
    ) -> NSShadow {
        let shadow = NSShadow()
        shadow.shadowColor = color
        shadow.shadowOffset = offset
        shadow.shadowBlurRadius = blurRadius
        return shadow
    }
}
#endif

// MARK: - Animation Support

extension ShadowStyle {
    
    /// 创建阴影动画
    func animated(duration: Double = 0.2) -> some View {
        EmptyView()
            .animation(.easeInOut(duration: duration), value: self.radius)
    }
}

// MARK: - Preview Support

#if DEBUG
struct Shadows_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(spacing: 30) {
                // 基础阴影展示
                Text("基础阴影系统")
                    .font(.headline)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 20) {
                    ShadowPreviewCard(title: "XS", shadow: Shadows.xs)
                    ShadowPreviewCard(title: "SM", shadow: Shadows.sm)
                    ShadowPreviewCard(title: "MD", shadow: Shadows.md)
                    ShadowPreviewCard(title: "LG", shadow: Shadows.lg)
                    ShadowPreviewCard(title: "XL", shadow: Shadows.xl)
                    ShadowPreviewCard(title: "XXL", shadow: Shadows.xxl)
                }
                
                // macOS 特定阴影
                Text("macOS 特定阴影")
                    .font(.headline)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 20) {
                    ShadowPreviewCard(title: "Window", shadow: Shadows.window)
                    ShadowPreviewCard(title: "Menu", shadow: Shadows.menu)
                    ShadowPreviewCard(title: "Toolbar", shadow: Shadows.toolbar)
                    ShadowPreviewCard(title: "Floating", shadow: Shadows.floatingPanel)
                }
                
                // 交互式阴影演示
                Text("交互式阴影")
                    .font(.headline)
                
                HStack(spacing: 20) {
                    Button("Hover Me") {
                        print("Button tapped")
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    .interactiveShadow()
                    
                    Rectangle()
                        .fill(Color.green)
                        .frame(width: 100, height: 60)
                        .cornerRadius(8)
                        .elevation(.level3)
                }
            }
            .padding()
        }
        .frame(width: 600, height: 800)
    }
}

struct ShadowPreviewCard: View {
    let title: String
    let shadow: ShadowStyle
    
    var body: some View {
        VStack {
            Rectangle()
                .fill(Color.white)
                .frame(height: 60)
                .cornerRadius(8)
                .shadow(shadow)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}
#endif