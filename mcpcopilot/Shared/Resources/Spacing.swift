//
//  Spacing.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

/// 应用统一的间距系统
struct Spacing {
    
    // MARK: - Base Spacing Units
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    static let xxxl: CGFloat = 64
    
    // MARK: - Component Specific Spacing
    
    /// 卡片内部间距
    struct Card {
        static let padding: CGFloat = Spacing.md
        static let spacing: CGFloat = Spacing.sm
        static let cornerRadius: CGFloat = 8
    }
    
    /// 按钮间距
    struct Button {
        static let padding: EdgeInsets = EdgeInsets(top: Spacing.sm, leading: Spacing.md, bottom: Spacing.sm, trailing: Spacing.md)
        static let spacing: CGFloat = Spacing.sm
        static let cornerRadius: CGFloat = 6
    }
    
    /// 表单间距
    struct Form {
        static let fieldSpacing: CGFloat = Spacing.md
        static let sectionSpacing: CGFloat = Spacing.lg
        static let labelSpacing: CGFloat = Spacing.xs
    }
    
    /// 列表间距
    struct List {
        static let itemSpacing: CGFloat = Spacing.xs
        static let sectionSpacing: CGFloat = Spacing.lg
        static let padding: CGFloat = Spacing.md
    }
    
    /// 工具栏间距
    struct Toolbar {
        static let height: CGFloat = 44
        static let padding: CGFloat = Spacing.md
        static let itemSpacing: CGFloat = Spacing.sm
    }
    
    /// 侧边栏间距
    struct Sidebar {
        static let width: CGFloat = 240
        static let minWidth: CGFloat = 200
        static let maxWidth: CGFloat = 300
        static let padding: CGFloat = Spacing.md
        static let itemSpacing: CGFloat = Spacing.xs
    }
    
    /// 内容区域间距
    struct Content {
        static let padding: CGFloat = Spacing.lg
        static let sectionSpacing: CGFloat = Spacing.xl
        static let itemSpacing: CGFloat = Spacing.md
    }
    
    /// 弹窗间距
    struct Modal {
        static let padding: CGFloat = Spacing.lg
        static let spacing: CGFloat = Spacing.md
        static let cornerRadius: CGFloat = 12
        static let minWidth: CGFloat = 400
        static let minHeight: CGFloat = 300
    }
    
    /// 状态指示器间距
    struct StatusIndicator {
        static let size: CGFloat = 8
        static let spacing: CGFloat = Spacing.xs
    }
    
    /// 图标间距
    struct Icon {
        static let small: CGFloat = 16
        static let medium: CGFloat = 20
        static let large: CGFloat = 24
        static let xlarge: CGFloat = 32
    }
}

// MARK: - Padding Extensions
extension View {
    
    /// 应用标准的内容区域内边距
    func contentPadding() -> some View {
        self.padding(Spacing.Content.padding)
    }
    
    /// 应用卡片内边距
    func cardPadding() -> some View {
        self.padding(Spacing.Card.padding)
    }
    
    /// 应用表单字段间距
    func formFieldSpacing() -> some View {
        self.padding(.bottom, Spacing.Form.fieldSpacing)
    }
    
    /// 应用工具栏内边距
    func toolbarPadding() -> some View {
        self.padding(.horizontal, Spacing.Toolbar.padding)
    }
    
    /// 应用侧边栏内边距
    func sidebarPadding() -> some View {
        self.padding(Spacing.Sidebar.padding)
    }
}

// MARK: - Spacing Helpers
extension Spacing {
    
    /// 根据层级获取间距
    static func forLevel(_ level: Int) -> CGFloat {
        switch level {
        case 0:
            return xs
        case 1:
            return sm
        case 2:
            return md
        case 3:
            return lg
        case 4:
            return xl
        case 5:
            return xxl
        default:
            return xxxl
        }
    }
    
    /// 获取响应式间距（根据窗口大小调整）
    static func responsive(compact: CGFloat, regular: CGFloat) -> CGFloat {
        // 这里可以根据实际的窗口大小来决定
        // 暂时返回 regular 值
        return regular
    }
}
