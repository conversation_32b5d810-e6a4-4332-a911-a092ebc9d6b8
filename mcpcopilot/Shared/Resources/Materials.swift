//
//  Materials.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS Materials and Visual Effects system
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Material System

/// 统一的材质和视觉效果管理系统 - macOS 优化版本
struct Materials {
    
    // MARK: - Basic Materials
    
    /// 透明材质
    static let clear = MaterialStyle(
        blur: .none,
        tint: .clear,
        opacity: 0.0
    )
    
    /// 超薄材质 - 轻微模糊效果
    static let ultraThin = MaterialStyle(
        blur: .ultraThin,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.8
    )
    
    /// 薄材质 - 适度模糊效果
    static let thin = MaterialStyle(
        blur: .thin,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.85
    )
    
    /// 常规材质 - 标准模糊效果
    static let regular = MaterialStyle(
        blur: .regular,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.9
    )
    
    /// 厚材质 - 强模糊效果
    static let thick = MaterialStyle(
        blur: .thick,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.95
    )
    
    /// 超厚材质 - 极强模糊效果
    static let ultraThick = MaterialStyle(
        blur: .ultraThick,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 1.0
    )
    
    // MARK: - macOS Specific Materials
    
    /// 窗口背景材质
    static let windowBackground = MaterialStyle(
        blur: .regular,
        tint: DesignTokens.color.backgroundWindow.value,
        opacity: 0.95
    )
    
    /// 侧边栏材质
    static let sidebar = MaterialStyle(
        blur: .thin,
        tint: DesignTokens.color.surfaceSidebar.value,
        opacity: 0.9
    )
    
    /// 工具栏材质
    static let toolbar = MaterialStyle(
        blur: .ultraThin,
        tint: DesignTokens.color.surfaceToolbar.value,
        opacity: 0.85
    )
    
    /// 菜单材质
    static let menu = MaterialStyle(
        blur: .regular,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.95
    )
    
    /// 弹出框材质
    static let popover = MaterialStyle(
        blur: .thick,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.98
    )
    
    /// HUD 材质 - 用于悬浮显示
    static let hud = MaterialStyle(
        blur: .regular,
        tint: Color.black,
        opacity: 0.8
    )
    
    /// 通知材质
    static let notification = MaterialStyle(
        blur: .regular,
        tint: DesignTokens.color.backgroundPrimary.value,
        opacity: 0.95
    )
    
    // MARK: - Contextual Materials
    
    /// 内容背景材质
    static let contentBackground = MaterialStyle(
        blur: .none,
        tint: DesignTokens.color.surfaceContent.value,
        opacity: 1.0
    )
    
    /// 卡片材质
    static let card = MaterialStyle(
        blur: .ultraThin,
        tint: DesignTokens.color.surfaceCard.value,
        opacity: 0.9
    )
    
    /// 浮动面板材质
    static let floatingPanel = MaterialStyle(
        blur: .regular,
        tint: DesignTokens.color.surfaceFloating.value,
        opacity: 0.95
    )
    
    /// 覆盖层材质
    static let overlay = MaterialStyle(
        blur: .thin,
        tint: Color.black,
        opacity: 0.3
    )
    
    /// 模态背景材质
    static let modalBackground = MaterialStyle(
        blur: .regular,
        tint: Color.black,
        opacity: 0.4
    )
}

// MARK: - Material Style Definition

struct MaterialStyle {
    let blur: BlurStyle
    let tint: Color
    let opacity: Double
    
    init(blur: BlurStyle, tint: Color, opacity: Double) {
        self.blur = blur
        self.tint = tint
        self.opacity = opacity
    }
}

// MARK: - Blur Style

enum BlurStyle {
    case none
    case ultraThin
    case thin
    case regular
    case thick
    case ultraThick
    
    #if canImport(AppKit)
    var nsVisualEffectMaterial: NSVisualEffectView.Material {
        switch self {
        case .none:
            return .windowBackground
        case .ultraThin:
            return .ultraThin
        case .thin:
            return .thin
        case .regular:
            return .medium
        case .thick:
            return .thick
        case .ultraThick:
            return .ultraThick
        }
    }
    #endif
}

// MARK: - Material Extensions

extension View {
    
    /// 应用材质效果
    func material(_ style: MaterialStyle) -> some View {
        self.modifier(MaterialModifier(style: style))
    }
    
    /// 应用窗口背景材质
    func windowMaterial() -> some View {
        self.material(Materials.windowBackground)
    }
    
    /// 应用侧边栏材质
    func sidebarMaterial() -> some View {
        self.material(Materials.sidebar)
    }
    
    /// 应用工具栏材质
    func toolbarMaterial() -> some View {
        self.material(Materials.toolbar)
    }
    
    /// 应用卡片材质
    func cardMaterial() -> some View {
        self.material(Materials.card)
    }
    
    /// 应用浮动面板材质
    func floatingMaterial() -> some View {
        self.material(Materials.floatingPanel)
    }
    
    /// 应用 HUD 材质
    func hudMaterial() -> some View {
        self.material(Materials.hud)
    }
}

// MARK: - Material Modifier

struct MaterialModifier: ViewModifier {
    let style: MaterialStyle
    
    func body(content: Content) -> some View {
        content
            .background(
                MaterialBackground(style: style)
            )
    }
}

// MARK: - Material Background

struct MaterialBackground: View {
    let style: MaterialStyle
    
    var body: some View {
        if style.blur == .none {
            // 无模糊效果，直接使用颜色
            style.tint.opacity(style.opacity)
        } else {
            // 使用 SwiftUI 的内置材质效果
            #if os(macOS)
            if #available(macOS 12.0, *) {
                Rectangle()
                    .fill(.regularMaterial)
                    .overlay(
                        style.tint.opacity(style.opacity - 0.5)
                    )
            } else {
                // macOS 11 及以下版本的回退方案
                VisualEffectBlur(material: style.blur.nsVisualEffectMaterial)
                    .overlay(
                        style.tint.opacity(style.opacity - 0.3)
                    )
            }
            #else
            style.tint.opacity(style.opacity)
            #endif
        }
    }
}

// MARK: - Visual Effect Blur (macOS)

#if canImport(AppKit)
struct VisualEffectBlur: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode
    let state: NSVisualEffectView.State
    
    init(
        material: NSVisualEffectView.Material = .medium,
        blendingMode: NSVisualEffectView.BlendingMode = .behindWindow,
        state: NSVisualEffectView.State = .active
    ) {
        self.material = material
        self.blendingMode = blendingMode
        self.state = state
    }
    
    func makeNSView(context: Context) -> NSVisualEffectView {
        let view = NSVisualEffectView()
        view.material = material
        view.blendingMode = blendingMode
        view.state = state
        return view
    }
    
    func updateNSView(_ nsView: NSVisualEffectView, context: Context) {
        nsView.material = material
        nsView.blendingMode = blendingMode
        nsView.state = state
    }
}

// MARK: - Advanced Visual Effects

struct AdvancedVisualEffect: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode
    let emphasized: Bool
    let maskImage: NSImage?
    
    init(
        material: NSVisualEffectView.Material = .medium,
        blendingMode: NSVisualEffectView.BlendingMode = .behindWindow,
        emphasized: Bool = false,
        maskImage: NSImage? = nil
    ) {
        self.material = material
        self.blendingMode = blendingMode
        self.emphasized = emphasized
        self.maskImage = maskImage
    }
    
    func makeNSView(context: Context) -> NSVisualEffectView {
        let view = NSVisualEffectView()
        view.material = material
        view.blendingMode = blendingMode
        view.isEmphasized = emphasized
        view.maskImage = maskImage
        return view
    }
    
    func updateNSView(_ nsView: NSVisualEffectView, context: Context) {
        nsView.material = material
        nsView.blendingMode = blendingMode
        nsView.isEmphasized = emphasized
        nsView.maskImage = maskImage
    }
}
#endif

// MARK: - Material Collections

extension Materials {
    
    /// 获取所有基础材质
    static var basicMaterials: [String: MaterialStyle] {
        return [
            "ultraThin": ultraThin,
            "thin": thin,
            "regular": regular,
            "thick": thick,
            "ultraThick": ultraThick
        ]
    }
    
    /// 获取所有 macOS 特定材质
    static var macOSMaterials: [String: MaterialStyle] {
        return [
            "windowBackground": windowBackground,
            "sidebar": sidebar,
            "toolbar": toolbar,
            "menu": menu,
            "popover": popover,
            "hud": hud,
            "notification": notification
        ]
    }
    
    /// 获取所有上下文材质
    static var contextualMaterials: [String: MaterialStyle] {
        return [
            "contentBackground": contentBackground,
            "card": card,
            "floatingPanel": floatingPanel,
            "overlay": overlay,
            "modalBackground": modalBackground
        ]
    }
}

// MARK: - Material Animations

extension MaterialStyle {
    
    /// 创建材质过渡动画
    func transition(to newStyle: MaterialStyle, duration: Double = 0.3) -> AnyTransition {
        return .asymmetric(
            insertion: .opacity.combined(with: .scale(scale: 0.95)),
            removal: .opacity.combined(with: .scale(scale: 1.05))
        )
    }
}

// MARK: - Adaptive Materials

struct AdaptiveMaterial {
    let lightMaterial: MaterialStyle
    let darkMaterial: MaterialStyle
    
    var current: MaterialStyle {
        #if canImport(AppKit)
        let appearance = NSApp.effectiveAppearance
        if appearance.bestMatch(from: [.darkAqua, .aqua]) == .darkAqua {
            return darkMaterial
        } else {
            return lightMaterial
        }
        #else
        return lightMaterial
        #endif
    }
    
    static let adaptiveWindow = AdaptiveMaterial(
        lightMaterial: Materials.windowBackground,
        darkMaterial: MaterialStyle(
            blur: .regular,
            tint: Color(red: 0.1, green: 0.1, blue: 0.1),
            opacity: 0.95
        )
    )
    
    static let adaptiveSidebar = AdaptiveMaterial(
        lightMaterial: Materials.sidebar,
        darkMaterial: MaterialStyle(
            blur: .thin,
            tint: Color(red: 0.15, green: 0.15, blue: 0.15),
            opacity: 0.9
        )
    )
}

extension View {
    
    /// 应用自适应材质
    func adaptiveMaterial(_ material: AdaptiveMaterial) -> some View {
        self.material(material.current)
    }
}

// MARK: - Preview Support

#if DEBUG
struct Materials_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // 背景图片
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [.blue, .purple, .pink],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            VStack(spacing: 30) {
                Text("Material Effects Demo")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // 基础材质展示
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 20) {
                    ForEach(Array(Materials.basicMaterials.keys), id: \.self) { key in
                        MaterialPreviewCard(
                            title: key.capitalized,
                            material: Materials.basicMaterials[key]!
                        )
                    }
                }
                
                // macOS 特定材质
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 20) {
                    MaterialPreviewCard(title: "Sidebar", material: Materials.sidebar)
                    MaterialPreviewCard(title: "Toolbar", material: Materials.toolbar)
                    MaterialPreviewCard(title: "HUD", material: Materials.hud)
                    MaterialPreviewCard(title: "Floating", material: Materials.floatingPanel)
                }
            }
            .padding()
        }
        .frame(width: 800, height: 700)
    }
}

struct MaterialPreviewCard: View {
    let title: String
    let material: MaterialStyle
    
    var body: some View {
        VStack {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Material Effect")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .material(material)
        .cornerRadius(12)
    }
}
#endif