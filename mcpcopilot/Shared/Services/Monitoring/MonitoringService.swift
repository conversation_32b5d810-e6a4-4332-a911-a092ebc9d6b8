//
//  MonitoringService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class MonitoringService: MonitoringServiceProtocol {
    
    // MARK: - Properties
    @Published private(set) var systemResourceInfo: SystemResourceInfo = SystemResourceInfo()
    @Published private(set) var processResourceInfos: [UUID: ProcessResourceInfo] = [:]
    
    private var monitoringTimer: Timer?
    private let monitoringInterval: TimeInterval = 2.0
    private let loggingService: LoggingServiceProtocol?
    
    // MARK: - Initialization
    init(loggingService: LoggingServiceProtocol? = nil) {
        self.loggingService = loggingService
    }
    
    // MARK: - Monitoring Control
    func startMonitoring() {
        guard monitoringTimer == nil else { return }
        
        Task {
            await loggingService?.log("Starting system monitoring", level: .info, source: "MonitoringService")
        }
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { _ in
            Task { @MainActor in
                await self.updateSystemResourceInfo()
                await self.updateProcessResourceInfos()
            }
        }
        
        // 立即执行一次更新
        Task {
            await updateSystemResourceInfo()
            await updateProcessResourceInfos()
        }
    }
    
    func stopMonitoring() {
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        Task {
            await loggingService?.log("Stopped system monitoring", level: .info, source: "MonitoringService")
        }
    }
    
    func getProcessResourceInfo(for pid: Int32) -> ProcessResourceInfo? {
        return processResourceInfos.values.first { $0.pid == pid }
    }
    
    // MARK: - Private Methods
    private func updateSystemResourceInfo() async {
        let cpuUsage = await getSystemCPUUsage()
        let memoryInfo = await getSystemMemoryInfo()
        
        systemResourceInfo = SystemResourceInfo(
            cpuUsage: cpuUsage,
            memoryUsage: memoryInfo.usage,
            totalMemory: memoryInfo.total,
            usedMemory: memoryInfo.used,
            timestamp: Date()
        )
    }
    
    private func updateProcessResourceInfos() async {
        // 这里应该根据实际运行的进程来更新
        // 暂时使用模拟数据
        var newProcessInfos: [UUID: ProcessResourceInfo] = [:]
        
        // 获取所有运行中的进程信息
        // 实际实现中，这里应该从 ProcessService 获取运行中的进程列表
        
        processResourceInfos = newProcessInfos
    }
    
    private func getSystemCPUUsage() async -> Double {
        // 使用 sysctl 获取 CPU 使用率
        // 这是一个简化的实现，实际应用中需要更复杂的计算
        return Double.random(in: 0...100) // 模拟数据
    }
    
    private func getSystemMemoryInfo() async -> (total: UInt64, used: UInt64, usage: Double) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let totalMemory = UInt64(ProcessInfo.processInfo.physicalMemory)
            let usedMemory = UInt64(info.resident_size)
            let usage = Double(usedMemory) / Double(totalMemory) * 100.0
            
            return (total: totalMemory, used: usedMemory, usage: usage)
        } else {
            // 返回模拟数据
            let totalMemory: UInt64 = 16 * 1024 * 1024 * 1024 // 16GB
            let usedMemory: UInt64 = UInt64.random(in: 4 * 1024 * 1024 * 1024...12 * 1024 * 1024 * 1024)
            let usage = Double(usedMemory) / Double(totalMemory) * 100.0
            
            return (total: totalMemory, used: usedMemory, usage: usage)
        }
    }
    
    private func getProcessResourceInfo(for pid: Int32) async -> ProcessResourceInfo? {
        // 获取特定进程的资源信息
        // 这里需要使用系统调用来获取进程信息
        
        let task = mach_task_self_
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(task,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return ProcessResourceInfo(
                pid: pid,
                cpuUsage: Double.random(in: 0...50), // 模拟 CPU 使用率
                memoryUsage: UInt64(info.resident_size),
                status: .running,
                uptime: TimeInterval.random(in: 0...3600), // 模拟运行时间
                timestamp: Date()
            )
        }
        
        return nil
    }
}

// MARK: - Helper Extensions
extension MonitoringService {
    func formatMemorySize(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    func formatCPUUsage(_ usage: Double) -> String {
        return String(format: "%.1f%%", usage)
    }
}
