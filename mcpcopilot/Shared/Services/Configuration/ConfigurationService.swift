//
//  ConfigurationService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class ConfigurationService: ConfigurationServiceProtocol {
    
    // MARK: - Properties
    @Published private(set) var currentConfiguration: AppConfiguration = .default
    
    var autoInitializeEngine: Bool {
        return true // Default to true for automatic engine initialization
    }
    
    func getAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    private let configurationFileName = "app_configuration.json"
    private var configurationURL: URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent("MCPCopilot").appendingPathComponent(configurationFileName)
    }
    
    // MARK: - Initialization
    func initialize() async {
        await createConfigurationDirectoryIfNeeded()
        do {
            currentConfiguration = try await loadConfiguration()
        } catch {
            print("Failed to load configuration, using default: \(error)")
            currentConfiguration = .default
            try? await saveConfiguration(currentConfiguration)
        }
    }
    
    // MARK: - Configuration Management
    func loadConfiguration() async throws -> AppConfiguration {
        guard FileManager.default.fileExists(atPath: configurationURL.path) else {
            return .default
        }
        
        let data = try Data(contentsOf: configurationURL)
        let configuration = try JSONDecoder().decode(AppConfiguration.self, from: data)
        return configuration
    }
    
    func saveConfiguration(_ config: AppConfiguration) async throws {
        await createConfigurationDirectoryIfNeeded()
        
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        let data = try encoder.encode(config)
        
        try data.write(to: configurationURL)
        currentConfiguration = config
    }
    
    func getDefaultConfiguration() -> AppConfiguration {
        return .default
    }
    
    // MARK: - Private Methods
    private func createConfigurationDirectoryIfNeeded() async {
        let directoryURL = configurationURL.deletingLastPathComponent()
        
        if !FileManager.default.fileExists(atPath: directoryURL.path) {
            try? FileManager.default.createDirectory(
                at: directoryURL,
                withIntermediateDirectories: true,
                attributes: nil
            )
        }
    }
}

// MARK: - Configuration Extensions
extension AppConfiguration {
    var yamcpCommand: String {
        return "yamcp"
    }
    
    var supergatewayCommand: String {
        return "supergateway"
    }
    
    var configurationDirectory: URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent("MCPCopilot")
    }
    
    var scenesConfigurationFile: URL {
        return configurationDirectory.appendingPathComponent("scenes.json")
    }
    
    var logsDirectory: URL {
        return configurationDirectory.appendingPathComponent("logs")
    }
}
