//
//  AnimationService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  Adapted from focusflyer with macOS optimizations
//

import Combine
import Foundation
import SwiftUI

#if canImport(RiveRuntime)
import RiveRuntime
#endif

// MARK: - Animation Data Model

public struct AnimationData {
    public let name: String
    public let duration: TimeInterval
    public let isLooping: Bool
    public let filePath: String
    
    public init(name: String, duration: TimeInterval, isLooping: Bool, filePath: String) {
        self.name = name
        self.duration = duration
        self.isLooping = isLooping
        self.filePath = filePath
    }
}

// MARK: - Animation Service Protocol

protocol AnimationServiceProtocol {
    func playAnimation(named name: String) async
    func stopAnimation() async
    func pauseAnimation() async
    func resumeAnimation() async
    func getAvailableAnimations() -> [AnimationData]
    func preloadAnimations()
    func clearCache()
}

// MARK: - Animation Service Implementation

/// 动画服务实现 - 管理 Rive 动画的播放和控制 (macOS optimized)
@MainActor
final class AnimationService: AnimationServiceProtocol, ObservableObject {
    
    // MARK: - Published Properties
    @Published private(set) var isAnimationPlaying: Bool = false
    @Published private(set) var currentAnimation: String?
    @Published private(set) var animationProgress: Double = 0.0
    
    // MARK: - Private Properties
    private let loggingService: LoggingServiceProtocol
    private var riveViewModel: RiveViewModel?
    private var animationCache: [String: RiveViewModel] = [:]
    private var cancellables = Set<AnyCancellable>()
    private var progressTimer: Timer?
    
    // MARK: - Animation Configuration
    private let animationDirectory = "RiveAssets"
    private let defaultAnimationFile = "mcp_loader"
    private let availableAnimations = [
        "mcp_loader",
        "process_indicator",
        "success_checkmark",
        "error_indicator",
        "loading_spinner"
    ]
    
    // MARK: - Initialization
    
    init(loggingService: LoggingServiceProtocol) {
        self.loggingService = loggingService
        self.loggingService.logInfo("🎬 AnimationService 初始化完成 (macOS)")
        
        setupAnimationObservers()
        preloadAnimations()
    }
    
    // MARK: - Public Methods
    
    func playAnimation(named name: String) async {
        loggingService.logInfo("▶️ 播放动画: \(name)")
        
        let startTime = Date()
        
        // 停止当前动画
        if isAnimationPlaying {
            await stopAnimation()
        }
        
        #if canImport(RiveRuntime)
        // 创建或获取RiveViewModel
        let viewModel = getOrCreateViewModel(for: name)
        self.riveViewModel = viewModel
        
        // 播放动画
        viewModel.play()
        
        // 启动进度跟踪
        startProgressTracking()
        #endif
        
        self.currentAnimation = name
        self.isAnimationPlaying = true
        
        let duration = Date().timeIntervalSince(startTime)
        loggingService.logPerformance(operation: "Animation Play", duration: duration)
        loggingService.logInfo("✅ 动画播放成功: \(name)")
    }
    
    func stopAnimation() async {
        guard isAnimationPlaying else { return }
        
        loggingService.logInfo("⏹️ 停止动画")
        
        #if canImport(RiveRuntime)
        riveViewModel?.stop()
        #endif
        
        stopProgressTracking()
        
        self.isAnimationPlaying = false
        self.currentAnimation = nil
        self.riveViewModel = nil
        self.animationProgress = 0.0
        
        loggingService.logInfo("✅ 动画已停止")
    }
    
    func pauseAnimation() async {
        guard isAnimationPlaying else { return }
        
        loggingService.logInfo("⏸️ 暂停动画")
        
        #if canImport(RiveRuntime)
        riveViewModel?.pause()
        #endif
        
        stopProgressTracking()
        self.isAnimationPlaying = false
        
        loggingService.logInfo("✅ 动画已暂停")
    }
    
    func resumeAnimation() async {
        guard let currentAnimation = currentAnimation, !isAnimationPlaying else { return }
        
        loggingService.logInfo("▶️ 恢复动画: \(currentAnimation)")
        
        #if canImport(RiveRuntime)
        riveViewModel?.play()
        #endif
        
        startProgressTracking()
        self.isAnimationPlaying = true
        
        loggingService.logInfo("✅ 动画恢复播放")
    }
    
    // MARK: - Animation Loading
    
    private func getOrCreateViewModel(for name: String) -> RiveViewModel {
        // 首先检查缓存
        if let cachedViewModel = animationCache[name] {
            loggingService.logInfo("📦 从缓存获取动画ViewModel: \(name)")
            return cachedViewModel
        }
        
        // 创建新的ViewModel
        #if canImport(RiveRuntime)
        let viewModel = RiveViewModel(
            fileName: name,
            autoPlay: false  // 不自动播放，由服务控制
        )
        #else
        // 创建模拟的 ViewModel 以避免编译错误
        let viewModel = MockRiveViewModel(fileName: name)
        #endif
        
        // 缓存ViewModel
        animationCache[name] = viewModel
        
        loggingService.logInfo("✅ 创建新的动画ViewModel: \(name)")
        return viewModel
    }
    
    // MARK: - Animation Management
    
    func getAvailableAnimations() -> [AnimationData] {
        return availableAnimations.map { name in
            AnimationData(
                name: name,
                duration: getAnimationDuration(for: name),
                isLooping: getAnimationLooping(for: name),
                filePath: "RiveAssets/\(name).riv"
            )
        }
    }
    
    func preloadAnimations() {
        loggingService.logInfo("📚 预加载动画资源")
        
        // 预创建关键动画的ViewModel
        let keyAnimations = [defaultAnimationFile, "process_indicator", "loading_spinner"]
        
        for animation in keyAnimations {
            let _ = getOrCreateViewModel(for: animation)
        }
        
        loggingService.logInfo("📚 动画预加载完成，已缓存 \(animationCache.count) 个动画")
    }
    
    func clearCache() {
        loggingService.logInfo("🗑️ 清理动画缓存")
        
        // 停止当前动画
        Task {
            await stopAnimation()
        }
        
        animationCache.removeAll()
        
        loggingService.logInfo("✅ 动画缓存已清理")
    }
    
    // MARK: - Progress Tracking
    
    private func startProgressTracking() {
        stopProgressTracking()
        
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateProgress()
            }
        }
    }
    
    private func stopProgressTracking() {
        progressTimer?.invalidate()
        progressTimer = nil
    }
    
    private func updateProgress() {
        #if canImport(RiveRuntime)
        // 这里需要根据实际的 Rive API 来获取进度
        // 由于 RiveRuntime 的具体 API 可能不同，这里提供一个示例实现
        if let viewModel = riveViewModel {
            // animationProgress = viewModel.progress // 假设的 API
        }
        #endif
    }
    
    // MARK: - Animation Utilities
    
    private func getAnimationDuration(for name: String) -> TimeInterval {
        // 根据动画名称返回预设的持续时间
        switch name {
        case "mcp_loader":
            return 2.0
        case "process_indicator":
            return 1.5
        case "success_checkmark":
            return 1.0
        case "error_indicator":
            return 1.0
        case "loading_spinner":
            return 0.8
        default:
            return 1.0
        }
    }
    
    private func getAnimationLooping(for name: String) -> Bool {
        // 根据动画名称返回是否循环
        switch name {
        case "mcp_loader", "process_indicator", "loading_spinner":
            return true
        case "success_checkmark", "error_indicator":
            return false
        default:
            return true
        }
    }
}

// MARK: - Animation Statistics

extension AnimationService {
    
    struct AnimationStatistics {
        let totalAnimations: Int
        let cachedAnimations: Int
        let currentAnimation: String?
        let isPlaying: Bool
        let progress: Double
        let cacheMemoryUsage: UInt64
    }
    
    func getStatistics() -> AnimationStatistics {
        return AnimationStatistics(
            totalAnimations: getAvailableAnimations().count,
            cachedAnimations: animationCache.count,
            currentAnimation: currentAnimation,
            isPlaying: isAnimationPlaying,
            progress: animationProgress,
            cacheMemoryUsage: estimateCacheMemoryUsage()
        )
    }
    
    private func estimateCacheMemoryUsage() -> UInt64 {
        // 估算缓存内存使用量
        return UInt64(animationCache.count * 1024 * 1024)  // 每个动画估计 1MB
    }
}

// MARK: - Error Handling

extension AnimationService {
    
    enum AnimationError: LocalizedError {
        case fileNotFound(String)
        case loadFailed(String, String)
        case playFailed(String)
        case notSupported(String)
        case riveNotAvailable
        
        var errorDescription: String? {
            switch self {
            case .fileNotFound(let name):
                return "动画文件未找到: \(name)"
            case .loadFailed(let name, let reason):
                return "动画加载失败: \(name) - \(reason)"
            case .playFailed(let name):
                return "动画播放失败: \(name)"
            case .notSupported(let format):
                return "不支持的动画格式: \(format)"
            case .riveNotAvailable:
                return "Rive 动画框架不可用"
            }
        }
    }
    
    func handleError(_ error: AnimationError) {
        loggingService.logError("动画错误: \(error.localizedDescription)")
        
        // 停止当前动画
        Task {
            await stopAnimation()
        }
    }
}

// MARK: - Animation Events

extension AnimationService {
    
    /// 设置动画事件监听
    func setupAnimationObservers() {
        loggingService.logInfo("🔔 设置动画事件监听")
        
        // 监听应用进入后台
        NotificationCenter.default.publisher(for: NSApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.pauseAnimation()
                }
            }
            .store(in: &cancellables)
        
        // 监听应用回到前台
        NotificationCenter.default.publisher(for: NSApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    if self?.currentAnimation != nil && self?.isAnimationPlaying == false {
                        await self?.resumeAnimation()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    /// 处理动画完成事件
    private func onAnimationCompleted(_ animationName: String) {
        loggingService.logInfo("🏁 动画播放完成: \(animationName)")
        
        Task { @MainActor in
            self.isAnimationPlaying = false
            self.animationProgress = 1.0
            
            if self.currentAnimation == animationName && !getAnimationLooping(for: animationName) {
                self.currentAnimation = nil
                self.riveViewModel = nil
            }
        }
    }
}

// MARK: - Mock RiveViewModel (for when RiveRuntime is not available)

#if !canImport(RiveRuntime)
class MockRiveViewModel {
    let fileName: String
    
    init(fileName: String) {
        self.fileName = fileName
    }
    
    func play() {
        // Mock implementation
    }
    
    func stop() {
        // Mock implementation
    }
    
    func pause() {
        // Mock implementation
    }
}

typealias RiveViewModel = MockRiveViewModel
#endif