//
//  CacheService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine


/// 缓存过期策略
enum CacheExpiration {
    case never
    case seconds(TimeInterval)
    case minutes(Int)
    case hours(Int)
    case days(Int)
    case date(Date)
    
    var expirationDate: Date? {
        switch self {
        case .never:
            return nil
        case .seconds(let seconds):
            return Date().addingTimeInterval(seconds)
        case .minutes(let minutes):
            return Date().addingTimeInterval(TimeInterval(minutes * 60))
        case .hours(let hours):
            return Date().addingTimeInterval(TimeInterval(hours * 3600))
        case .days(let days):
            return Date().addingTimeInterval(TimeInterval(days * 86400))
        case .date(let date):
            return date
        }
    }
}

/// 缓存项包装器
private struct CacheItem<T: Codable>: Codable {
    let value: T
    let expirationDate: Date?
    let createdAt: Date
    
    init(value: T, expiration: CacheExpiration?) {
        self.value = value
        self.expirationDate = expiration?.expirationDate
        self.createdAt = Date()
    }
    
    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return Date() > expirationDate
    }
}

/// 缓存服务实现
@MainActor
final class CacheService: CacheServiceProtocol {
    
    // MARK: - Properties
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    private let maxCacheSize: Int64 = 100 * 1024 * 1024 // 100MB
    
    // MARK: - Initialization
    init() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsPath.appendingPathComponent("MCPCopilot/Cache")
        
        setupCacheDirectory()
        setupEncoder()
        
        // 启动时清理过期项
        Task {
            try? await cleanExpiredItems()
        }
    }
    
    // MARK: - Public Methods
    func store<T: Codable>(_ object: T, forKey key: String, expiration: CacheExpiration? = nil) async throws {
        let cacheItem = CacheItem(value: object, expiration: expiration)
        let data = try encoder.encode(cacheItem)
        let url = cacheFileURL(for: key)
        
        try data.write(to: url)
        
        // 检查缓存大小并清理
        await checkCacheSizeAndCleanup()
    }
    
    func retrieve<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T? {
        let url = cacheFileURL(for: key)
        
        guard fileManager.fileExists(atPath: url.path) else {
            return nil
        }
        
        let data = try Data(contentsOf: url)
        let cacheItem = try decoder.decode(CacheItem<T>.self, from: data)
        
        // 检查是否过期
        if cacheItem.isExpired {
            try await remove(forKey: key)
            return nil
        }
        
        return cacheItem.value
    }
    
    func remove(forKey key: String) async throws {
        let url = cacheFileURL(for: key)
        
        if fileManager.fileExists(atPath: url.path) {
            try fileManager.removeItem(at: url)
        }
    }
    
    func removeAll() async throws {
        let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
        
        for url in contents {
            try fileManager.removeItem(at: url)
        }
    }
    
    func exists(forKey key: String) -> Bool {
        let url = cacheFileURL(for: key)
        return fileManager.fileExists(atPath: url.path)
    }
    
    func cacheSize() async -> Int64 {
        do {
            let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            
            var totalSize: Int64 = 0
            for url in contents {
                let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            }
            
            return totalSize
        } catch {
            return 0
        }
    }
    
    func cleanExpiredItems() async throws {
        let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
        
        for url in contents {
            do {
                let data = try Data(contentsOf: url)
                
                // 尝试解码为通用缓存项来检查过期时间
                if let cacheItem = try? decoder.decode(GenericCacheItem.self, from: data),
                   cacheItem.isExpired {
                    try fileManager.removeItem(at: url)
                }
            } catch {
                // 如果无法解码，可能是损坏的文件，删除它
                try? fileManager.removeItem(at: url)
            }
        }
    }
    
    // MARK: - Private Methods
    private func setupCacheDirectory() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
    }
    
    private func setupEncoder() {
        encoder.dateEncodingStrategy = .iso8601
        decoder.dateDecodingStrategy = .iso8601
    }
    
    private func cacheFileURL(for key: String) -> URL {
        let hashedKey = key.sha256
        return cacheDirectory.appendingPathComponent("\(hashedKey).cache")
    }
    
    private func checkCacheSizeAndCleanup() async {
        let currentSize = await cacheSize()
        
        if currentSize > maxCacheSize {
            await cleanupOldestItems()
        }
    }
    
    private func cleanupOldestItems() async {
        do {
            let contents = try fileManager.contentsOfDirectory(
                at: cacheDirectory,
                includingPropertiesForKeys: [.contentModificationDateKey]
            )
            
            // 按修改时间排序，删除最旧的文件
            let sortedContents = contents.sorted { url1, url2 in
                let date1 = (try? url1.resourceValues(forKeys: [.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                let date2 = (try? url2.resourceValues(forKeys: [.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                return date1 < date2
            }
            
            // 删除最旧的 25% 文件
            let itemsToDelete = sortedContents.prefix(sortedContents.count / 4)
            for url in itemsToDelete {
                try? fileManager.removeItem(at: url)
            }
        } catch {
            print("Failed to cleanup cache: \(error)")
        }
    }
}

// MARK: - Generic Cache Item for Expiration Check
private struct GenericCacheItem: Codable {
    let expirationDate: Date?
    let createdAt: Date
    
    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return Date() > expirationDate
    }
}

// MARK: - String Hashing Extension
private extension String {
    var sha256: String {
        let data = self.data(using: .utf8) ?? Data()
        var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        
        data.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(data.count), &hash)
        }
        
        return hash.map { String(format: "%02x", $0) }.joined()
    }
}

// MARK: - Cache Extensions
extension CacheService {
    
    /// 缓存图片数据
    func cacheImage(data: Data, forKey key: String, expiration: CacheExpiration = .days(7)) async throws {
        try await store(data, forKey: "image_\(key)", expiration: expiration)
    }
    
    /// 获取缓存的图片数据
    func getCachedImage(forKey key: String) async throws -> Data? {
        return try await retrieve(Data.self, forKey: "image_\(key)")
    }
    
    /// 缓存配置数据
    func cacheConfiguration<T: Codable>(_ config: T, forKey key: String) async throws {
        try await store(config, forKey: "config_\(key)", expiration: .never)
    }
    
    /// 获取缓存的配置数据
    func getCachedConfiguration<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T? {
        return try await retrieve(type, forKey: "config_\(key)")
    }
    
    /// 缓存临时数据
    func cacheTemporary<T: Codable>(_ object: T, forKey key: String, minutes: Int = 30) async throws {
        try await store(object, forKey: "temp_\(key)", expiration: .minutes(minutes))
    }
    
    /// 获取临时缓存数据
    func getTemporary<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T? {
        return try await retrieve(type, forKey: "temp_\(key)")
    }
}

// MARK: - Import CommonCrypto for SHA256
import CommonCrypto
