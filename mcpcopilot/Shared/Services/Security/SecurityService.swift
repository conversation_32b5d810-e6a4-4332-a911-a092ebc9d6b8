//
//  SecurityService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Security
import CryptoKit
import Combine


/// 安全错误类型
enum SecurityError: LocalizedError {
    case keychainError(OSStatus)
    case encryptionError
    case decryptionError
    case invalidKey
    case dataCorrupted
    
    var errorDescription: String? {
        switch self {
        case .keychainError(let status):
            return "Keychain error: \(status)"
        case .encryptionError:
            return "Encryption failed"
        case .decryptionError:
            return "Decryption failed"
        case .invalidKey:
            return "Invalid key"
        case .dataCorrupted:
            return "Data corrupted"
        }
    }
}

/// 安全服务实现
@MainActor
final class SecurityService: SecurityServiceProtocol {
    
    // MARK: - Properties
    private let serviceName = "com.mcpcopilot.keychain"
    private let accessGroup: String?
    
    // MARK: - Initialization
    init(accessGroup: String? = nil) {
        self.accessGroup = accessGroup
    }
    
    // MARK: - Keychain Operations
    func storeSecureData(_ data: Data, forKey key: String) throws {
        // 删除现有项（如果存在）
        try? deleteSecureData(forKey: key)
        
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw SecurityError.keychainError(status)
        }
    }
    
    func retrieveSecureData(forKey key: String) throws -> Data? {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                return nil
            }
            throw SecurityError.keychainError(status)
        }
        
        return result as? Data
    }
    
    func deleteSecureData(forKey key: String) throws {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw SecurityError.keychainError(status)
        }
    }
    
    // MARK: - Encryption/Decryption
    func encryptData(_ data: Data, withKey key: SymmetricKey) throws -> Data {
        do {
            let sealedBox = try AES.GCM.seal(data, using: key)
            return sealedBox.combined ?? Data()
        } catch {
            throw SecurityError.encryptionError
        }
    }
    
    func decryptData(_ encryptedData: Data, withKey key: SymmetricKey) throws -> Data {
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            return try AES.GCM.open(sealedBox, using: key)
        } catch {
            throw SecurityError.decryptionError
        }
    }
    
    func generateSymmetricKey() -> SymmetricKey {
        return SymmetricKey(size: .bits256)
    }
    
    // MARK: - Hashing
    func hashData(_ data: Data) -> String {
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    // MARK: - API Key Validation
    func validateAPIKey(_ apiKey: String) -> Bool {
        // 基本的 API Key 格式验证
        let trimmedKey = apiKey.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查长度（通常 API Key 至少 20 个字符）
        guard trimmedKey.count >= 20 else { return false }
        
        // 检查是否包含有效字符
        let validCharacterSet = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "-_"))
        guard trimmedKey.rangeOfCharacter(from: validCharacterSet.inverted) == nil else { return false }
        
        return true
    }
}

// MARK: - Convenience Methods
extension SecurityService {
    
    /// 存储 API Key
    func storeAPIKey(_ apiKey: String, forService service: String) throws {
        guard validateAPIKey(apiKey) else {
            throw SecurityError.invalidKey
        }
        
        let data = apiKey.data(using: .utf8) ?? Data()
        try storeSecureData(data, forKey: "api_key_\(service)")
    }
    
    /// 获取 API Key
    func getAPIKey(forService service: String) throws -> String? {
        guard let data = try retrieveSecureData(forKey: "api_key_\(service)") else {
            return nil
        }
        
        return String(data: data, encoding: .utf8)
    }
    
    /// 删除 API Key
    func deleteAPIKey(forService service: String) throws {
        try deleteSecureData(forKey: "api_key_\(service)")
    }
    
    /// 存储加密的配置
    func storeEncryptedConfiguration<T: Codable>(_ config: T, forKey key: String) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(config)
        
        let encryptionKey = generateSymmetricKey()
        let encryptedData = try encryptData(data, withKey: encryptionKey)
        
        // 存储加密密钥
        try storeSecureData(encryptionKey.withUnsafeBytes { Data($0) }, forKey: "\(key)_key")
        
        // 存储加密数据
        try storeSecureData(encryptedData, forKey: key)
    }
    
    /// 获取加密的配置
    func getEncryptedConfiguration<T: Codable>(_ type: T.Type, forKey key: String) throws -> T? {
        guard let encryptedData = try retrieveSecureData(forKey: key),
              let keyData = try retrieveSecureData(forKey: "\(key)_key") else {
            return nil
        }
        
        let encryptionKey = SymmetricKey(data: keyData)
        let decryptedData = try decryptData(encryptedData, withKey: encryptionKey)
        
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: decryptedData)
    }
    
    /// 生成安全的随机字符串
    func generateSecureRandomString(length: Int = 32) -> String {
        let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        var result = ""
        
        for _ in 0..<length {
            let randomIndex = Int.random(in: 0..<characters.count)
            let character = characters[characters.index(characters.startIndex, offsetBy: randomIndex)]
            result.append(character)
        }
        
        return result
    }
    
    /// 验证数据完整性
    func verifyDataIntegrity(_ data: Data, expectedHash: String) -> Bool {
        let actualHash = hashData(data)
        return actualHash == expectedHash
    }
    
    /// 安全地比较两个字符串（防止时序攻击）
    func secureCompare(_ string1: String, _ string2: String) -> Bool {
        guard string1.count == string2.count else { return false }
        
        let data1 = string1.data(using: .utf8) ?? Data()
        let data2 = string2.data(using: .utf8) ?? Data()
        
        var result = 0
        for i in 0..<data1.count {
            result |= Int(data1[i] ^ data2[i])
        }
        
        return result == 0
    }
}

// MARK: - Environment Variables Security
extension SecurityService {
    
    /// 安全地设置环境变量（避免在日志中暴露）
    func setSecureEnvironmentVariable(_ value: String, forKey key: String) {
        setenv(key, value, 1)
    }
    
    /// 获取环境变量并清理
    func getAndClearEnvironmentVariable(forKey key: String) -> String? {
        guard let value = getenv(key) else { return nil }
        let stringValue = String(cString: value)
        unsetenv(key)
        return stringValue
    }
    
    /// 清理敏感环境变量
    func clearSensitiveEnvironmentVariables() {
        let sensitiveKeys = [
            "API_KEY",
            "SECRET_KEY",
            "PASSWORD",
            "TOKEN",
            "PRIVATE_KEY"
        ]
        
        for key in sensitiveKeys {
            unsetenv(key)
        }
    }
}
