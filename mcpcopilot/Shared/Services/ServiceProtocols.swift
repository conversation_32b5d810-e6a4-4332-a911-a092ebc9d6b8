//
//  ServiceProtocols.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine
import CryptoKit
import SwiftUI

// MARK: - Configuration Service Protocol
@MainActor
protocol ConfigurationServiceProtocol: ObservableObject {
    var autoInitializeEngine: Bool { get }

    func initialize() async
    func loadConfiguration() async throws -> AppConfiguration
    func saveConfiguration(_ config: AppConfiguration) async throws
    func getDefaultConfiguration() -> AppConfiguration
    func getAppVersion() -> String
}

// MARK: - Process Service Protocol
@MainActor
protocol ProcessServiceProtocol: ObservableObject {
    var runningProcesses: [String: Process] { get }

    func startScene(_ scene: MCPScene) async throws
    func stopScene(_ scene: MCPScene) async throws
    func restartScene(_ scene: MCPScene) async throws
    func getProcessStatus(for sceneId: UUID) -> ProcessStatus
    func killAllProcesses() async
}

// MARK: - Monitoring Service Protocol
@MainActor
protocol MonitoringServiceProtocol: ObservableObject {
    var systemResourceInfo: SystemResourceInfo { get }
    var processResourceInfos: [UUID: ProcessResourceInfo] { get }

    func startMonitoring()
    func stopMonitoring()
    func getProcessResourceInfo(for pid: Int32) -> ProcessResourceInfo?
}

// MARK: - Logging Service Protocol
@MainActor
protocol LoggingServiceProtocol: ObservableObject {
    var logEntries: [LogEntry] { get }

    func initialize() async
    func log(_ message: String, level: LogLevel, source: String?) async
    func clearLogs()
    func exportLogs() async throws -> URL

    // Convenience methods for different log levels
    func logInfo(_ message: String) async
    func logWarning(_ message: String) async
    func logError(_ message: String) async
    func logPerformance(operation: String, duration: TimeInterval) async
    func logUserInteraction(_ message: String) async
}

// MARK: - Storage Service Protocol
@MainActor
protocol StorageServiceProtocol: ObservableObject {
    func initialize() async
    func save<T: Codable>(_ object: T, to key: String) async throws
    func load<T: Codable>(_ type: T.Type, from key: String) async throws -> T?
    func delete(key: String) async throws
    func exists(key: String) -> Bool
}

// MARK: - Localization Service Protocol
protocol LocalizationServiceProtocol: ObservableObject {
    var currentLanguage: String { get }
    var availableLanguages: [Language] { get }

    func setLanguage(_ languageCode: String)
    func localizedString(for key: String, defaultValue: String?) -> String
    func localizedString(for key: String, arguments: CVarArg...) -> String
    func translateText(_ text: String, from sourceLanguage: String?) async -> String
    func translateTexts(_ texts: [String], from sourceLanguage: String?) async -> [String]
}

// MARK: - Performance Monitoring Service Protocol
@MainActor
protocol PerformanceMonitoringServiceProtocol: ObservableObject {
    var isMonitoring: Bool { get }
    var performanceMetrics: PerformanceMetrics { get }
    var memoryWarningThreshold: Double { get set }
    var cpuWarningThreshold: Double { get set }

    func startMonitoring()
    func stopMonitoring()
    func recordCustomMetric(name: String, value: Double, unit: String)
    func recordExecutionTime<T>(for operation: String, block: () throws -> T) rethrows -> T
    func recordAsyncExecutionTime<T>(for operation: String, block: () async throws -> T) async rethrows -> T
}

// MARK: - Cache Service Protocol
@MainActor
protocol CacheServiceProtocol: ObservableObject {
    func store<T: Codable>(_ object: T, forKey key: String, expiration: CacheExpiration?) async throws
    func retrieve<T: Codable>(_ type: T.Type, forKey key: String) async throws -> T?
    func remove(forKey key: String) async throws
    func removeAll() async throws
    func exists(forKey key: String) -> Bool
    func cacheSize() async -> Int64
    func cleanExpiredItems() async throws
}

// MARK: - Security Service Protocol
@MainActor
protocol SecurityServiceProtocol: ObservableObject {
    func storeSecureData(_ data: Data, forKey key: String) throws
    func retrieveSecureData(forKey key: String) throws -> Data?
    func deleteSecureData(forKey key: String) throws
    func encryptData(_ data: Data, withKey key: SymmetricKey) throws -> Data
    func decryptData(_ encryptedData: Data, withKey key: SymmetricKey) throws -> Data
    func generateSymmetricKey() -> SymmetricKey
    func hashData(_ data: Data) -> String
    func validateAPIKey(_ apiKey: String) -> Bool
}

// MARK: - Repository Protocols

@MainActor
protocol SceneRepositoryProtocol: ObservableObject {
    var scenes: [MCPScene] { get }

    func loadScenes() async throws
    func saveScene(_ scene: MCPScene) async throws
    func deleteScene(_ scene: MCPScene) async throws
    func getScene(by id: UUID) -> MCPScene?
}

@MainActor
protocol ConfigurationRepositoryProtocol: ObservableObject {
    var appConfiguration: AppConfiguration { get }

    func loadConfiguration() async throws
    func saveConfiguration(_ config: AppConfiguration) async throws
    func resetToDefault() async throws
}

// MARK: - App Configuration
struct AppConfiguration: Codable {
    var defaultPort: Int
    var autoStart: Bool
    var logLevel: LogLevel
    var maxRestartAttempts: Int
    var monitoringInterval: TimeInterval
    var enableSystemTray: Bool
    var language: String

    static let `default` = AppConfiguration(
        defaultPort: 7700,
        autoStart: false,
        logLevel: .info,
        maxRestartAttempts: 3,
        monitoringInterval: 5.0,
        enableSystemTray: true,
        language: "zh-Hans"
    )
}

// MARK: - Game Engine Service Protocol
@MainActor
protocol GameEngineServiceProtocol: ObservableObject {
    var engineState: GameEngineState { get }
    var currentScene: String { get }
    var availableScenes: [String] { get }

    func initialize() async
    func switchScene(to sceneName: String) async
    func suspend() async
    func resume() async
    func shutdown() async
    func getDiagnostics() -> EngineDiagnostics
    func generateDiagnosticReport() -> String
}

// MARK: - Game Engine State
public enum GameEngineState: Equatable {
    case initializing
    case ready
    case error(String)
    case suspended
}

// MARK: - Engine Diagnostics
struct EngineDiagnostics {
    let isInitialized: Bool
    let currentState: GameEngineState
    let currentScene: String
    let availableScenes: [String]
    let packFileExists: Bool
    let packFilePath: String?
    let memoryUsage: UInt64
    let initializationTime: TimeInterval?
    let godotVersion: String
}
