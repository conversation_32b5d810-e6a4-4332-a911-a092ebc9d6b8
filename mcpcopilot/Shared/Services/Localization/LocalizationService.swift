//
//  LocalizationService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine
import SwiftUI
import Translation

/// 语言模型
struct Language: Identifiable, Codable {
    let id = UUID()
    let code: String
    let name: String
    let nativeName: String
    
    static let supportedLanguages = [
        Language(code: "zh-Hans", name: "Chinese Simplified", nativeName: "简体中文"),
        Language(code: "en", name: "English", nativeName: "English"),
        Language(code: "de", name: "German", nativeName: "Deutsch"),
        Language(code: "fr", name: "French", nativeName: "Français"),
        Language(code: "ja", name: "Japanese", nativeName: "日本語")
    ]
}

/// 本地化服务实现
final class LocalizationService: LocalizationServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var currentLanguage: String = "zh-Hans"
    
    // MARK: - Properties
    let availableLanguages = Language.supportedLanguages
    private var bundle: Bundle = Bundle.main
    private var translationSession: TranslationSession?
    
    // MARK: - Initialization
    init() {
        setupInitialLanguage()
    }
    
    // MARK: - Public Methods
    func setLanguage(_ languageCode: String) {
        guard availableLanguages.contains(where: { $0.code == languageCode }) else {
            print("Unsupported language: \(languageCode)")
            return
        }
        
        currentLanguage = languageCode
        updateBundle()
        saveLanguagePreference()
        
        // 通知语言变更
        NotificationCenter.default.post(
            name: .languageDidChange,
            object: nil,
            userInfo: ["language": languageCode]
        )
    }
    
    func localizedString(for key: String, defaultValue: String? = nil) -> String {
        let localizedString = bundle.localizedString(forKey: key, value: nil, table: nil)
        
        // 如果没有找到本地化字符串，返回默认值或 key
        if localizedString == key {
            return defaultValue ?? key
        }
        
        return localizedString
    }
    
    func localizedString(for key: String, arguments: CVarArg...) -> String {
        let format = localizedString(for: key)
        return String(format: format, arguments: arguments)
    }
    
    /// 自动翻译文本到当前语言
    func translateText(_ text: String, from sourceLanguage: String? = nil) async -> String {
        guard #available(macOS 15.0, *) else {
            return text
        }
        
        do {
            let sourceLanguageCode = sourceLanguage ?? "en"
            let targetLanguageCode = currentLanguage
            
            if sourceLanguageCode == targetLanguageCode {
                return text
            }
            
            let configuration = TranslationSession.Configuration(
                source: Locale.Language(identifier: sourceLanguageCode),
                target: Locale.Language(identifier: targetLanguageCode)
            )
            
            if translationSession == nil {
                translationSession = TranslationSession(configuration: configuration)
            }
            
            let response = try await translationSession?.translate(text)
            return response?.targetText ?? text
        } catch {
            print("Translation error: \(error)")
            return text
        }
    }
    
    /// 批量翻译多个文本
    func translateTexts(_ texts: [String], from sourceLanguage: String? = nil) async -> [String] {
        await withTaskGroup(of: (Int, String).self) { group in
            for (index, text) in texts.enumerated() {
                group.addTask {
                    let translated = await self.translateText(text, from: sourceLanguage)
                    return (index, translated)
                }
            }
            
            var results: [String] = Array(repeating: "", count: texts.count)
            for await (index, translatedText) in group {
                results[index] = translatedText
            }
            return results
        }
    }
    
    // MARK: - Private Methods
    private func setupInitialLanguage() {
        // 从用户偏好中读取语言设置
        if let savedLanguage = UserDefaults.standard.string(forKey: "AppLanguage"),
           availableLanguages.contains(where: { $0.code == savedLanguage }) {
            currentLanguage = savedLanguage
        } else {
            // 使用系统语言或默认语言
            currentLanguage = detectSystemLanguage()
        }
        
        updateBundle()
    }
    
    private func detectSystemLanguage() -> String {
        let preferredLanguages = Locale.preferredLanguages
        
        for preferredLanguage in preferredLanguages {
            let languageCode = String(preferredLanguage.prefix(2))
            
            // 检查是否支持该语言
            if availableLanguages.contains(where: { $0.code.hasPrefix(languageCode) }) {
                return availableLanguages.first { $0.code.hasPrefix(languageCode) }?.code ?? "zh-Hans"
            }
        }
        
        return "zh-Hans" // 默认语言
    }
    
    private func updateBundle() {
        guard let path = Bundle.main.path(forResource: currentLanguage, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            print("Failed to load bundle for language: \(currentLanguage)")
            self.bundle = Bundle.main
            return
        }
        
        self.bundle = bundle
    }
    
    private func saveLanguagePreference() {
        UserDefaults.standard.set(currentLanguage, forKey: "AppLanguage")
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let languageDidChange = Notification.Name("LanguageDidChange")
}

// MARK: - Localization Extensions
extension String {
    
    /// 获取本地化字符串
    var localized: String {
        let localizationService = DIContainer.shared.localizationService
        return localizationService.localizedString(for: self, defaultValue: nil)
    }
    
    /// 获取带参数的本地化字符串
    func localized(with arguments: CVarArg...) -> String {
        let localizationService = DIContainer.shared.localizationService
        return localizationService.localizedString(for: self, arguments: arguments)
    }
    
    /// 获取带默认值的本地化字符串
    func localized(defaultValue: String) -> String {
        let localizationService = DIContainer.shared.localizationService
        return localizationService.localizedString(for: self, defaultValue: defaultValue)
    }
}

// MARK: - SwiftUI Extensions
extension Text {
    
    /// 创建本地化文本
    init(localized key: String) {
        self.init(key.localized)
    }
    
    /// 创建带参数的本地化文本
    init(localized key: String, arguments: CVarArg...) {
        self.init(key.localized(with: arguments))
    }
}

// MARK: - Environment Key
struct LocalizationServiceKey: EnvironmentKey {
    static let defaultValue: any LocalizationServiceProtocol = LocalizationService()
}

extension EnvironmentValues {
    var localizationService: any LocalizationServiceProtocol {
        get { self[LocalizationServiceKey.self] }
        set { self[LocalizationServiceKey.self] = newValue }
    }
}

// MARK: - View Modifier
struct LocalizedViewModifier: ViewModifier {
    @Environment(\.localizationService) private var localizationService
    
    func body(content: Content) -> some View {
        content
            .environment(\.locale, Locale(identifier: localizationService.currentLanguage))
    }
}

extension View {
    func localized() -> some View {
        self.modifier(LocalizedViewModifier())
    }
}
