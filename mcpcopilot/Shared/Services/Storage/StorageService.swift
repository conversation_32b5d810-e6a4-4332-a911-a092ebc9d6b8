//
//  StorageService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class StorageService: StorageServiceProtocol {
    
    // MARK: - Properties
    private let baseDirectory: URL
    private let fileManager = FileManager.default
    
    // MARK: - Initialization
    init() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.baseDirectory = documentsPath.appendingPathComponent("MCPCopilot")
    }
    
    func initialize() async {
        await createBaseDirectoryIfNeeded()
    }
    
    // MARK: - Storage Operations
    func save<T: Codable>(_ object: T, to key: String) async throws {
        await createBaseDirectoryIfNeeded()
        
        let url = baseDirectory.appendingPathComponent("\(key).json")
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601
        
        let data = try encoder.encode(object)
        try data.write(to: url)
    }
    
    func load<T: Codable>(_ type: T.Type, from key: String) async throws -> T? {
        let url = baseDirectory.appendingPathComponent("\(key).json")
        
        guard fileManager.fileExists(atPath: url.path) else {
            return nil
        }
        
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        return try decoder.decode(type, from: data)
    }
    
    func delete(key: String) async throws {
        let url = baseDirectory.appendingPathComponent("\(key).json")
        
        if fileManager.fileExists(atPath: url.path) {
            try fileManager.removeItem(at: url)
        }
    }
    
    func exists(key: String) -> Bool {
        let url = baseDirectory.appendingPathComponent("\(key).json")
        return fileManager.fileExists(atPath: url.path)
    }
    
    // MARK: - Directory Management
    func createDirectory(at path: String) async throws {
        let url = baseDirectory.appendingPathComponent(path)
        
        if !fileManager.fileExists(atPath: url.path) {
            try fileManager.createDirectory(
                at: url,
                withIntermediateDirectories: true,
                attributes: nil
            )
        }
    }
    
    func getFileURL(for key: String) -> URL {
        return baseDirectory.appendingPathComponent("\(key).json")
    }
    
    func getDirectoryURL(for path: String) -> URL {
        return baseDirectory.appendingPathComponent(path)
    }
    
    // MARK: - Private Methods
    private func createBaseDirectoryIfNeeded() async {
        if !fileManager.fileExists(atPath: baseDirectory.path) {
            try? fileManager.createDirectory(
                at: baseDirectory,
                withIntermediateDirectories: true,
                attributes: nil
            )
        }
    }
}

// MARK: - Storage Keys
extension StorageService {
    enum Keys {
        static let appConfiguration = "app_configuration"
        static let scenes = "scenes"
        static let servers = "servers"
        static let logs = "logs"
    }
}
