//
//  ProcessService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class ProcessService: ProcessServiceProtocol {
    
    // MARK: - Properties
    @Published private(set) var runningProcesses: [String: Process] = [:]
    
    private var processStatuses: [UUID: ProcessStatus] = [:]
    private let loggingService: (any LoggingServiceProtocol)?
    
    // MARK: - Initialization
    init(loggingService: (any LoggingServiceProtocol)? = nil) {
        self.loggingService = loggingService
    }
    
    // MARK: - Scene Management
    func startScene(_ scene: MCPScene) async throws {
        let sceneKey = scene.id.uuidString
        
        // 检查是否已经在运行
        if let existingProcess = runningProcesses[sceneKey], existingProcess.isRunning {
            await loggingService?.log("Scene \(scene.name) is already running", level: .warning, source: "ProcessService")
            return
        }
        
        processStatuses[scene.id] = .starting
        await loggingService?.log("Starting scene: \(scene.name)", level: .info, source: "ProcessService")
        
        do {
            // 1. 首先启动 yamcp server
            let _ = try await startYamcpServer(for: scene)
            
            // 2. 然后启动 supergateway
            let supergatewayProcess = try await startSupergateway(for: scene)
            
            // 存储进程引用
            runningProcesses[sceneKey] = supergatewayProcess
            processStatuses[scene.id] = .running
            
            await loggingService?.log("Scene \(scene.name) started successfully on port \(scene.port)", level: .info, source: "ProcessService")
            
        } catch {
            processStatuses[scene.id] = .error
            await loggingService?.log("Failed to start scene \(scene.name): \(error)", level: .error, source: "ProcessService")
            throw error
        }
    }
    
    func stopScene(_ scene: MCPScene) async throws {
        let sceneKey = scene.id.uuidString
        
        guard let process = runningProcesses[sceneKey] else {
            await loggingService?.log("Scene \(scene.name) is not running", level: .warning, source: "ProcessService")
            return
        }
        
        processStatuses[scene.id] = .stopping
        await loggingService?.log("Stopping scene: \(scene.name)", level: .info, source: "ProcessService")
        
        // 终止进程
        process.terminate()
        
        // 等待进程结束
        process.waitUntilExit()
        
        runningProcesses.removeValue(forKey: sceneKey)
        processStatuses[scene.id] = .stopped
        
        await loggingService?.log("Scene \(scene.name) stopped", level: .info, source: "ProcessService")
    }
    
    func restartScene(_ scene: MCPScene) async throws {
        await loggingService?.log("Restarting scene: \(scene.name)", level: .info, source: "ProcessService")
        
        try await stopScene(scene)
        
        // 等待一秒钟确保进程完全停止
        try await Task.sleep(nanoseconds: 1_000_000_000)
        
        try await startScene(scene)
    }
    
    func getProcessStatus(for sceneId: UUID) -> ProcessStatus {
        return processStatuses[sceneId] ?? .stopped
    }
    
    func killAllProcesses() async {
        await loggingService?.log("Killing all processes", level: .info, source: "ProcessService")
        
        for (_, process) in runningProcesses {
            process.terminate()
            process.waitUntilExit()
        }
        
        runningProcesses.removeAll()
        processStatuses.removeAll()
        
        await loggingService?.log("All processes killed", level: .info, source: "ProcessService")
    }
    
    // MARK: - Private Methods
    private func startYamcpServer(for scene: MCPScene) async throws -> Process {
        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/local/bin/yamcp")
        process.arguments = ["run", scene.name]
        
        // 设置环境变量
        var environment = ProcessInfo.processInfo.environment
        for (key, value) in scene.environment {
            environment[key] = value
        }
        process.environment = environment
        
        // 设置输出管道
        let outputPipe = Pipe()
        let errorPipe = Pipe()
        process.standardOutput = outputPipe
        process.standardError = errorPipe
        
        // 监听输出
        setupProcessOutputMonitoring(process: process, sceneName: scene.name, outputPipe: outputPipe, errorPipe: errorPipe)
        
        try process.run()
        
        await loggingService?.log("yamcp server started for scene: \(scene.name)", level: .info, source: "ProcessService")
        
        return process
    }
    
    private func startSupergateway(for scene: MCPScene) async throws -> Process {
        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/local/bin/supergateway")
        process.arguments = [
            "--stdio",
            "yamcp run \(scene.name)",
            "--port",
            "\(scene.port)"
        ]
        
        // 设置输出管道
        let outputPipe = Pipe()
        let errorPipe = Pipe()
        process.standardOutput = outputPipe
        process.standardError = errorPipe
        
        // 监听输出
        setupProcessOutputMonitoring(process: process, sceneName: "supergateway-\(scene.name)", outputPipe: outputPipe, errorPipe: errorPipe)
        
        try process.run()
        
        await loggingService?.log("supergateway started for scene: \(scene.name) on port \(scene.port)", level: .info, source: "ProcessService")
        
        return process
    }
    
    private func setupProcessOutputMonitoring(process: Process, sceneName: String, outputPipe: Pipe, errorPipe: Pipe) {
        // 监听标准输出
        outputPipe.fileHandleForReading.readabilityHandler = { handle in
            let data = handle.availableData
            if !data.isEmpty, let output = String(data: data, encoding: .utf8) {
                Task { @MainActor in
                    await self.loggingService?.log(output.trimmingCharacters(in: .whitespacesAndNewlines), level: .info, source: sceneName)
                }
            }
        }
        
        // 监听错误输出
        errorPipe.fileHandleForReading.readabilityHandler = { handle in
            let data = handle.availableData
            if !data.isEmpty, let output = String(data: data, encoding: .utf8) {
                Task { @MainActor in
                    await self.loggingService?.log(output.trimmingCharacters(in: .whitespacesAndNewlines), level: .error, source: sceneName)
                }
            }
        }
        
        // 监听进程终止
        process.terminationHandler = { process in
            Task { @MainActor in
                await self.loggingService?.log("Process \(sceneName) terminated with exit code: \(process.terminationStatus)", level: .info, source: "ProcessService")
            }
        }
    }
}
