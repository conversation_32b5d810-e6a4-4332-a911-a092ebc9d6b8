//
//  PerformanceMonitoringService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine
import os.log
import SwiftUI


/// 性能指标模型
struct PerformanceMetrics: Codable {
    var cpuUsage: Double = 0.0
    var memoryUsage: Double = 0.0
    var memoryPressure: MemoryPressure = .normal
    var diskUsage: Double = 0.0
    var networkLatency: Double = 0.0
    var frameRate: Double = 0.0
    var customMetrics: [String: MetricValue] = [:]
    var executionTimes: [String: TimeInterval] = [:]
    var timestamp: Date = Date()
    
    enum MemoryPressure: String, Codable, CaseIterable {
        case normal = "normal"
        case warning = "warning"
        case critical = "critical"
        
        var displayName: String {
            switch self {
            case .normal: return "正常"
            case .warning: return "警告"
            case .critical: return "严重"
            }
        }
        
        var color: String {
            switch self {
            case .normal: return "green"
            case .warning: return "orange"
            case .critical: return "red"
            }
        }
    }
}

/// 自定义指标值
struct MetricValue: Codable {
    let value: Double
    let unit: String
    let timestamp: Date
    
    init(value: Double, unit: String) {
        self.value = value
        self.unit = unit
        self.timestamp = Date()
    }
}

/// 性能监控服务实现
@MainActor
final class PerformanceMonitoringService: PerformanceMonitoringServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var isMonitoring = false
    @Published private(set) var performanceMetrics = PerformanceMetrics()
    @Published var memoryWarningThreshold: Double = 80.0
    @Published var cpuWarningThreshold: Double = 80.0
    
    // MARK: - Private Properties
    private var monitoringTimer: Timer?
    private let monitoringInterval: TimeInterval = 1.0
    private let logger = Logger(subsystem: "com.mcpcopilot.performance", category: "monitoring")
    private var startTime: Date = Date()
    
    // MARK: - Initialization
    init() {
        setupMemoryPressureMonitoring()
    }
    
    // MARK: - Public Methods
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startTime = Date()
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { _ in
            Task { @MainActor in
                await self.updateMetrics()
            }
        }
        
        logger.info("Performance monitoring started")
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        logger.info("Performance monitoring stopped")
    }
    
    func recordCustomMetric(name: String, value: Double, unit: String) {
        performanceMetrics.customMetrics[name] = MetricValue(value: value, unit: unit)
        logger.debug("Custom metric recorded: \(name) = \(value) \(unit)")
    }
    
    func recordExecutionTime<T>(for operation: String, block: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        
        performanceMetrics.executionTimes[operation] = executionTime
        logger.debug("Execution time for \(operation): \(executionTime)s")
        
        return result
    }
    
    func recordAsyncExecutionTime<T>(for operation: String, block: () async throws -> T) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await block()
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        
        performanceMetrics.executionTimes[operation] = executionTime
        logger.debug("Async execution time for \(operation): \(executionTime)s")
        
        return result
    }
    
    // MARK: - Private Methods
    private func updateMetrics() async {
        performanceMetrics.cpuUsage = await getCPUUsage()
        performanceMetrics.memoryUsage = await getMemoryUsage()
        performanceMetrics.memoryPressure = await getMemoryPressure()
        performanceMetrics.diskUsage = await getDiskUsage()
        performanceMetrics.timestamp = Date()
        
        // 检查警告阈值
        checkThresholds()
    }
    
    private func getCPUUsage() async -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            // 这里需要更复杂的 CPU 使用率计算
            // 暂时返回模拟数据
            return Double.random(in: 0...100)
        }
        
        return 0.0
    }
    
    private func getMemoryUsage() async -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let totalMemory = ProcessInfo.processInfo.physicalMemory
            let usedMemory = UInt64(info.resident_size)
            return Double(usedMemory) / Double(totalMemory) * 100.0
        }
        
        return 0.0
    }
    
    private func getMemoryPressure() async -> PerformanceMetrics.MemoryPressure {
        let memoryUsage = performanceMetrics.memoryUsage
        
        if memoryUsage > 90 {
            return .critical
        } else if memoryUsage > memoryWarningThreshold {
            return .warning
        } else {
            return .normal
        }
    }
    
    private func getDiskUsage() async -> Double {
        do {
            let homeURL = FileManager.default.homeDirectoryForCurrentUser
            let resourceValues = try homeURL.resourceValues(forKeys: [
                .volumeAvailableCapacityKey,
                .volumeTotalCapacityKey
            ])
            
            if let available = resourceValues.volumeAvailableCapacity,
               let total = resourceValues.volumeTotalCapacity {
                let used = total - available
                return Double(used) / Double(total) * 100.0
            }
        } catch {
            logger.error("Failed to get disk usage: \(error.localizedDescription)")
        }
        
        return 0.0
    }
    
    private func checkThresholds() {
        // CPU 警告
        if performanceMetrics.cpuUsage > cpuWarningThreshold {
            logger.warning("High CPU usage detected: \(self.performanceMetrics.cpuUsage)%")
            NotificationCenter.default.post(
                name: .performanceWarning,
                object: nil,
                userInfo: [
                    "type": "cpu",
                    "value": performanceMetrics.cpuUsage,
                    "threshold": cpuWarningThreshold
                ]
            )
        }
        
        // 内存警告
        if performanceMetrics.memoryUsage > memoryWarningThreshold {
            logger.warning("High memory usage detected: \(self.performanceMetrics.memoryUsage)%")
            NotificationCenter.default.post(
                name: .performanceWarning,
                object: nil,
                userInfo: [
                    "type": "memory",
                    "value": performanceMetrics.memoryUsage,
                    "threshold": memoryWarningThreshold
                ]
            )
        }
    }
    
    private func setupMemoryPressureMonitoring() {
        // 监听系统内存压力通知
        #if os(iOS)
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.logger.warning("System memory warning received")
            self?.performanceMetrics.memoryPressure = .critical
        }
        #endif
    }
    
    deinit {
        Task { @MainActor in
            stopMonitoring()
        }
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let performanceWarning = Notification.Name("PerformanceWarning")
}

// MARK: - Performance Measurement Extensions
extension PerformanceMonitoringService {
    
    /// 测量视图渲染时间
    func measureViewRenderTime<T: View>(_ view: T, name: String) -> some View {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        return view
            .onAppear {
                let renderTime = CFAbsoluteTimeGetCurrent() - startTime
                Task { @MainActor in
                    self.recordCustomMetric(name: "\(name)_render_time", value: renderTime, unit: "seconds")
                }
            }
    }
    
    /// 测量网络请求时间
    func measureNetworkRequest<T>(
        name: String,
        request: () async throws -> T
    ) async rethrows -> T {
        return try await recordAsyncExecutionTime(for: "network_\(name)") {
            try await request()
        }
    }
}
