//
//  GameEngineService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  Adapted from focusflyer with macOS optimizations
//

import Combine
import Foundation
import SwiftUI

#if canImport(SwiftGodotKit)
import SwiftGodotKit

// MARK: - GodotApp Wrapper for compatibility
class GodotApp {
    let packFile: String

    init(packFile: String) {
        self.packFile = packFile
    }
}
#else
// MARK: - Mock GodotApp for when SwiftGodotKit is not available
class GodotApp {
    let packFile: String

    init(packFile: String) {
        self.packFile = packFile
    }
}
#endif

// MARK: - Game Engine State (defined in ServiceProtocols.swift)

// MARK: - Game Engine Result Types

enum GameEngineResult<T> {
    case success(T)
    case failure(GameEngineError)
}

enum GameEngineError: LocalizedError {
    case packFileNotFound(String)
    case initializationFailed(String)
    case sceneNotFound(String)
    case engineNotReady
    case configurationError(String)
    case godotNotAvailable

    var errorDescription: String? {
        switch self {
        case .packFileNotFound(let fileName):
            return "资源包文件未找到: \(fileName)"
        case .initializationFailed(let reason):
            return "引擎初始化失败: \(reason)"
        case .sceneNotFound(let sceneName):
            return "场景未找到: \(sceneName)"
        case .engineNotReady:
            return "引擎尚未就绪"
        case .configurationError(let reason):
            return "配置错误: \(reason)"
        case .godotNotAvailable:
            return "Godot 引擎不可用"
        }
    }
}

// MARK: - Game Engine Service Protocol (defined in ServiceProtocols.swift)

// MARK: - Game Engine Service Implementation

/// 游戏引擎服务实现 - 管理 Godot 引擎的生命周期和状态 (macOS optimized)
@MainActor
final class GameEngineService: GameEngineServiceProtocol, ObservableObject {

    // MARK: - Published Properties
    @Published private(set) var godotApp: GodotApp?
    @Published private(set) var engineState: GameEngineState = .initializing
    @Published private(set) var currentScene: String = "main"
    @Published private(set) var availableScenes: [String] = []

    // MARK: - Private Properties
    private let loggingService: LoggingServiceProtocol
    private let configurationService: ConfigurationServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    private var hasStartedInitialization = false
    private var initializationStartTime: Date?

    // MARK: - Constants
    private let packFileName = "mcp_game.pck"
    private let defaultScene = "main"
    private let godotAssetsDirectory = "GodotAssets"

    // MARK: - Initialization

    init(loggingService: LoggingServiceProtocol, configurationService: ConfigurationServiceProtocol) {
        self.loggingService = loggingService
        self.configurationService = configurationService

        Task {
            await self.loggingService.logInfo("🎮 GameEngineService 初始化完成 (macOS)")
        }

        setupApplicationObservers()
        loadAvailableScenes()

        // 如果配置允许，自动初始化引擎
        if configurationService.autoInitializeEngine {
            autoInitializeEngine()
        }
    }

    // MARK: - Public Methods

    func initialize() async {
        guard !hasStartedInitialization else { return }
        hasStartedInitialization = true
        initializationStartTime = Date()

        await loggingService.logInfo("开始初始化Godot引擎 - macOS版本")
        engineState = .initializing

        // 检查Godot框架可用性
        #if !canImport(SwiftGodotKit)
        let errorMsg = "SwiftGodotKit 框架不可用"
        loggingService.logError(errorMsg)
        engineState = .error(errorMsg)
        return
        #endif

        // 检查PCK文件
        guard checkPCKFile() else {
            let errorMsg = "PCK文件未找到或损坏"
            loggingService.logError(errorMsg)
            engineState = .error(errorMsg)
            return
        }

        // 初始化引擎
        await performEngineInitialization()

        // 记录初始化时间
        if let startTime = initializationStartTime {
            let initTime = Date().timeIntervalSince(startTime)
            loggingService.logPerformance(operation: "Engine Initialization", duration: initTime)
        }
    }

    func switchScene(to sceneName: String) async {
        guard engineState == .ready else {
            loggingService.logWarning("引擎未就绪，无法切换场景")
            return
        }

        guard isSceneValid(sceneName) else {
            loggingService.logError("无效的场景名称: \(sceneName)")
            return
        }

        loggingService.logUserInteraction("切换场景到: \(sceneName)")

        // 更新当前场景记录
        currentScene = sceneName

        #if canImport(SwiftGodotKit)
        // 实际的场景切换需要通过Godot的信号系统或全局变量来实现
        // 这里只是记录场景状态，具体实现取决于Godot项目的设计
        #endif

        loggingService.logInfo("场景切换请求完成: \(sceneName)")
    }

    func reinitializeEngine() async {
        loggingService.logWarning("重新初始化引擎")

        // 清理当前状态
        await cleanupEngine()

        // 重置状态
        godotApp = nil
        engineState = .initializing
        hasStartedInitialization = false
        initializationStartTime = nil

        // 重新初始化
        await initialize()
    }

    func suspend() async {
        await suspendEngine()
    }

    func resume() async {
        await resumeEngine()
    }

    func shutdown() async {
        await cleanupEngine()
        engineState = .initializing
        hasStartedInitialization = false
        loggingService.logInfo("引擎已关闭")
    }

    func generateDiagnosticReport() -> String {
        return getResourceStatusReport()
    }

    func getEngineStatus() -> String {
        switch engineState {
        case .initializing:
            return "引擎正在初始化..."
        case .ready:
            return "引擎已就绪，当前场景: \(getSceneDisplayName(currentScene))"
        case .error(let message):
            return "引擎错误: \(message)"
        case .suspended:
            return "引擎已暂停"
        }
    }

    // MARK: - Private Methods

    private func autoInitializeEngine() {
        Task {
            await initialize()
        }
    }

    private func checkPCKFile() -> Bool {
        guard let pckPath = getPackFilePath() else {
            loggingService.logError("PCK文件未找到 - 所有搜索方式都失败")
            return false
        }

        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: pckPath)
            let fileSize = attributes[.size] as? Int64 ?? 0

            loggingService.logInfo("PCK文件检查通过 - 文件大小: \(fileSize) bytes, 路径: \(pckPath)")
            return fileSize > 0
        } catch {
            loggingService.logError("PCK文件检查失败: \(error)")
            return false
        }
    }

    private func performEngineInitialization() async {
        loggingService.logInfo("创建GodotApp实例 - macOS优化版本")

        // 获取正确的PCK文件路径
        guard let pckPath = getPackFilePath() else {
            let errorMsg = "无法获取PCK文件路径"
            loggingService.logError(errorMsg)
            engineState = .error(errorMsg)
            return
        }

        #if canImport(SwiftGodotKit)
        // 按照官方文档的方式创建 GodotApp
        let app = GodotApp(packFile: packFileName)

        // 设置引擎状态
        self.godotApp = app
        self.engineState = .ready
        self.currentScene = defaultScene

        loggingService.logInfo("Godot引擎初始化成功 - macOS版本")
        #else
        // 模拟初始化成功（当SwiftGodotKit不可用时）
        engineState = .ready
        loggingService.logWarning("Godot引擎模拟初始化 - SwiftGodotKit不可用")
        #endif
    }

    private func cleanupEngine() async {
        loggingService.logInfo("清理引擎资源")

        #if canImport(SwiftGodotKit)
        // 执行必要的清理工作
        godotApp = nil
        #endif

        // 清理缓存和状态
        currentScene = defaultScene

        loggingService.logInfo("引擎资源清理完成")
    }

    private func setupApplicationObservers() {
        // 监听应用进入后台
        NotificationCenter.default.publisher(for: NSApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.suspendEngine()
                }
            }
            .store(in: &cancellables)

        // 监听应用回到前台
        NotificationCenter.default.publisher(for: NSApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.resumeEngine()
                }
            }
            .store(in: &cancellables)
    }

    private func suspendEngine() async {
        if engineState == .ready {
            engineState = .suspended
            loggingService.logInfo("引擎已暂停")
        }
    }

    private func resumeEngine() async {
        if engineState == .suspended {
            engineState = .ready
            loggingService.logInfo("引擎已恢复")
        }
    }

    private func loadAvailableScenes() {
        // 加载可用场景列表
        availableScenes = [
            "main",
            "game",
            "menu",
            "settings",
            "debug",
            "animation_demo"
        ]

        loggingService.logInfo("加载了 \(availableScenes.count) 个可用场景")
    }
}

// MARK: - Scene Management

extension GameEngineService {

    func getAvailableScenes() -> [String] {
        return availableScenes
    }

    func isSceneValid(_ sceneName: String) -> Bool {
        return availableScenes.contains(sceneName)
    }

    func getSceneDisplayName(_ sceneName: String) -> String {
        switch sceneName {
        case "main":
            return "主场景"
        case "game":
            return "游戏场景"
        case "menu":
            return "菜单"
        case "settings":
            return "设置"
        case "debug":
            return "调试"
        case "animation_demo":
            return "动画演示"
        default:
            return sceneName.capitalized
        }
    }

    func getSceneDescription(_ sceneName: String) -> String {
        switch sceneName {
        case "main":
            return "应用主界面和导航"
        case "game":
            return "主要游戏内容"
        case "menu":
            return "菜单和选项"
        case "settings":
            return "设置和配置"
        case "debug":
            return "调试和开发工具"
        case "animation_demo":
            return "动画效果演示"
        default:
            return "场景描述"
        }
    }
}

// MARK: - Performance Monitoring

extension GameEngineService {

    struct EnginePerformanceMetrics {
        let frameRate: Double
        let memoryUsage: UInt64
        let cpuUsage: Double
        let renderTime: TimeInterval
        let updateTime: TimeInterval
        let engineUptime: TimeInterval
    }

    func getPerformanceMetrics() -> EnginePerformanceMetrics {
        let uptime = initializationStartTime?.timeIntervalSinceNow ?? 0

        return EnginePerformanceMetrics(
            frameRate: 60.0,  // 假设60fps
            memoryUsage: getMemoryUsage(),
            cpuUsage: getCurrentCPUUsage(),
            renderTime: 0.016,  // 16ms for 60fps
            updateTime: 0.008,  // 8ms for update
            engineUptime: abs(uptime)
        )
    }

    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4

        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        return result == KERN_SUCCESS ? info.resident_size : 0
    }

    private func getCurrentCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4

        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        // 简化的 CPU 使用率计算
        return result == KERN_SUCCESS ? Double(info.resident_size) / 1000000.0 : 0.0
    }
}

// MARK: - Diagnostics

extension GameEngineService {

    struct EngineDiagnostics {
        let isInitialized: Bool
        let currentState: GameEngineState
        let currentScene: String
        let availableScenes: [String]
        let packFileExists: Bool
        let packFilePath: String?
        let memoryUsage: UInt64
        let initializationTime: TimeInterval?
        let godotVersion: String
    }

    func getDiagnostics() -> EngineDiagnostics {
        let initTime = initializationStartTime?.timeIntervalSinceNow

        return EngineDiagnostics(
            isInitialized: godotApp != nil,
            currentState: engineState,
            currentScene: currentScene,
            availableScenes: availableScenes,
            packFileExists: getPackFilePath() != nil,
            packFilePath: getPackFilePath(),
            memoryUsage: getMemoryUsage(),
            initializationTime: initTime != nil ? abs(initTime!) : nil,
            godotVersion: getGodotVersion()
        )
    }

    private func getGodotVersion() -> String {
        #if canImport(SwiftGodotKit)
        return "SwiftGodotKit Available"
        #else
        return "SwiftGodotKit Not Available"
        #endif
    }

    private func getPackFilePath() -> String? {
        // 尝试多种方式查找PCK文件
        var pckPath: String?

        // 方式1: 从GodotAssets目录中查找
        pckPath = Bundle.main.path(forResource: "mcp_game", ofType: "pck", inDirectory: godotAssetsDirectory)

        // 方式2: 直接从bundle根目录查找
        if pckPath == nil {
            pckPath = Bundle.main.path(forResource: "mcp_game", ofType: "pck")
        }

        // 方式3: 从Resources目录查找
        if pckPath == nil {
            pckPath = Bundle.main.path(forResource: "mcp_game", ofType: "pck", inDirectory: "Resources/\(godotAssetsDirectory)")
        }

        return pckPath
    }

    func getResourceStatusReport() -> String {
        var report = "=== Godot引擎资源状态 (macOS) ===\n\n"

        // PCK文件状态
        if let pckPath = getPackFilePath() {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: pckPath)
                let fileSize = attributes[.size] as? Int64 ?? 0
                report += "📦 PCK文件: 正常 (\(fileSize) bytes)\n"
                report += "📍 路径: \(pckPath)\n"
            } catch {
                report += "📦 PCK文件: 错误 - \(error)\n"
            }
        } else {
            report += "📦 PCK文件: 未找到\n"
        }

        // 引擎状态
        report += "\n🎮 引擎状态: \(getEngineStatus())\n"
        report += "🎬 当前场景: \(getSceneDisplayName(currentScene))\n"
        report += "📋 可用场景: \(availableScenes.count) 个\n"

        // 系统信息
        report += "\n💻 系统信息:\n"
        report += "  macOS 版本: \(ProcessInfo.processInfo.operatingSystemVersionString)\n"
        report += "  应用版本: \(configurationService.getAppVersion())\n"
        report += "  Godot 框架: \(getGodotVersion())\n"

        return report
    }
}

// MARK: - Error Handling

extension GameEngineService {

    func handleError(_ error: GameEngineError) {
        loggingService.logError("引擎错误: \(error.localizedDescription)")

        Task {
            await updateEngineState(.error(error.localizedDescription))
        }
    }

    private func updateEngineState(_ newState: GameEngineState) async {
        self.engineState = newState
    }
}