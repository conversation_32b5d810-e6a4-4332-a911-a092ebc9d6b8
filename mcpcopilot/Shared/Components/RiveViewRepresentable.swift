//
//  RiveViewRepresentable.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  Adapted from focusflyer for macOS
//

import SwiftUI

#if canImport(RiveRuntime)
import RiveRuntime
#endif

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Rive View Representable

/// SwiftUI 包装器，用于在 macOS 中显示 Rive 动画
struct RiveViewRepresentable: NSViewRepresentable {
    let viewModel: RiveViewModel
    let fit: RiveFit
    let alignment: RiveAlignment
    
    init(
        viewModel: RiveViewModel,
        fit: RiveFit = .contain,
        alignment: RiveAlignment = .center
    ) {
        self.viewModel = viewModel
        self.fit = fit
        self.alignment = alignment
    }
    
    func makeNSView(context: Context) -> NSView {
        #if canImport(RiveRuntime)
        let riveView = RiveNSView()
        // Configure the view - API might differ, using fallback approach
        return riveView
        #else
        // 当 RiveRuntime 不可用时，创建一个模拟视图
        let mockView = MockRiveNSView()
        mockView.wantsLayer = true
        mockView.layer?.backgroundColor = NSColor.systemGray.cgColor
        return mockView
        #endif
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        #if canImport(RiveRuntime)
        // Update the view when needed
        #endif
    }
    
    private func setupAnimationEvents(riveView: RiveNSView, context: Context) {
        #if canImport(RiveRuntime)
        // 设置动画事件监听
        // 这里可以根据 RiveRuntime 的具体 API 来设置事件处理
        #endif
    }
}

// MARK: - Rive Animation View

/// 高级 Rive 动画视图，包含控制和状态管理
struct RiveAnimationView: View {
    let fileName: String
    let animationName: String?
    let autoPlay: Bool
    let loop: Bool
    let fit: RiveFit
    let alignment: RiveAlignment
    
    @State private var viewModel: RiveViewModel
    @State private var isPlaying: Bool = false
    @State private var showControls: Bool = false
    
    init(
        fileName: String,
        animationName: String? = nil,
        autoPlay: Bool = true,
        loop: Bool = true,
        fit: RiveFit = .contain,
        alignment: RiveAlignment = .center
    ) {
        self.fileName = fileName
        self.animationName = animationName
        self.autoPlay = autoPlay
        self.loop = loop
        self.fit = fit
        self.alignment = alignment
        
        #if canImport(RiveRuntime)
        self._viewModel = State(initialValue: RiveViewModel(fileName: fileName, autoPlay: autoPlay))
        #else
        self._viewModel = State(initialValue: MockRiveViewModel(fileName: fileName))
        #endif
    }
    
    var body: some View {
        ZStack {
            // 主动画视图
            RiveViewRepresentable(
                viewModel: viewModel,
                fit: fit,
                alignment: alignment
            )
            .onAppear {
                setupAnimation()
            }
            
            // 调试控制面板（仅在调试模式下显示）
            #if DEBUG
            if showControls {
                VStack {
                    Spacer()
                    
                    HStack(spacing: 12) {
                        Button(action: togglePlayback) {
                            Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.white)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(8)
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(20)
                        
                        Button(action: restartAnimation) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 16))
                                .foregroundColor(.white)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(8)
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(20)
                    }
                    .padding()
                }
            }
            #endif
        }
        .onTapGesture(count: 2) {
            #if DEBUG
            showControls.toggle()
            #endif
        }
    }
    
    private func setupAnimation() {
        #if canImport(RiveRuntime)
        if let animationName = animationName {
            viewModel.setAnimation(animationName)
        }
        
        if autoPlay {
            viewModel.play()
            isPlaying = true
        }
        #endif
    }
    
    private func togglePlayback() {
        #if canImport(RiveRuntime)
        if isPlaying {
            viewModel.pause()
        } else {
            viewModel.play()
        }
        isPlaying.toggle()
        #endif
    }
    
    private func restartAnimation() {
        #if canImport(RiveRuntime)
        viewModel.stop()
        viewModel.play()
        isPlaying = true
        #endif
    }
}

// MARK: - Rive Fit and Alignment

#if canImport(RiveRuntime)
// 使用 RiveRuntime 的类型
typealias RiveFit = RiveRuntime.RiveFit
typealias RiveAlignment = RiveRuntime.RiveAlignment
typealias RiveNSView = RiveRuntime.RiveView
typealias RiveViewModel = RiveRuntime.RiveViewModel
#else
// 模拟类型定义
enum RiveFit: String, CaseIterable {
    case fill = "fill"
    case contain = "contain"
    case cover = "cover"
    case fitWidth = "fitWidth"
    case fitHeight = "fitHeight"
    case none = "none"
}

enum RiveAlignment: String, CaseIterable {
    case topLeft = "topLeft"
    case topCenter = "topCenter"
    case topRight = "topRight"
    case centerLeft = "centerLeft"
    case center = "center"
    case centerRight = "centerRight"
    case bottomLeft = "bottomLeft"
    case bottomCenter = "bottomCenter"
    case bottomRight = "bottomRight"
}

class MockRiveNSView: NSView {
    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)
        
        // 绘制一个占位符
        NSColor.systemGray.setFill()
        dirtyRect.fill()
        
        // 绘制文本
        let attributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.systemFont(ofSize: 14),
            .foregroundColor: NSColor.labelColor
        ]
        
        let text = "Rive Animation\n(Framework Not Available)"
        let textSize = text.size(withAttributes: attributes)
        let textRect = NSRect(
            x: (dirtyRect.width - textSize.width) / 2,
            y: (dirtyRect.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        
        text.draw(in: textRect, withAttributes: attributes)
    }
}

class MockRiveViewModel {
    let fileName: String
    
    init(fileName: String, autoPlay: Bool = true) {
        self.fileName = fileName
    }
    
    func play() {
        print("Mock Rive: Playing animation")
    }
    
    func pause() {
        print("Mock Rive: Pausing animation")
    }
    
    func stop() {
        print("Mock Rive: Stopping animation")
    }
    
    func setAnimation(_ name: String) {
        print("Mock Rive: Setting animation to \(name)")
    }
}

typealias RiveNSView = MockRiveNSView
typealias RiveViewModel = MockRiveViewModel
#endif

// MARK: - Convenience Extensions

extension RiveAnimationView {
    /// 创建一个简单的 Rive 动画视图
    static func simple(fileName: String, autoPlay: Bool = true) -> RiveAnimationView {
        return RiveAnimationView(
            fileName: fileName,
            autoPlay: autoPlay,
            loop: true,
            fit: .contain,
            alignment: .center
        )
    }
    
    /// 创建一个适合图标的 Rive 动画视图
    static func icon(fileName: String, size: CGFloat = 24) -> some View {
        RiveAnimationView(
            fileName: fileName,
            autoPlay: true,
            loop: true,
            fit: .contain,
            alignment: .center
        )
        .frame(width: size, height: size)
    }
    
    /// 创建一个全屏的 Rive 动画视图
    static func fullscreen(fileName: String, autoPlay: Bool = true) -> RiveAnimationView {
        return RiveAnimationView(
            fileName: fileName,
            autoPlay: autoPlay,
            loop: true,
            fit: .cover,
            alignment: .center
        )
    }
}

// MARK: - Animation States

/// Rive 动画状态
enum RiveAnimationState {
    case stopped
    case playing
    case paused
    case completed
}

/// Rive 动画事件
enum RiveAnimationEvent {
    case started
    case paused
    case resumed
    case completed
    case looped
}

// MARK: - Preview Support

#if DEBUG
struct RiveViewRepresentable_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 简单动画
            RiveAnimationView.simple(fileName: "mcp_loader")
                .frame(height: 200)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            
            // 图标动画
            HStack(spacing: 16) {
                RiveAnimationView.icon(fileName: "success_checkmark", size: 32)
                RiveAnimationView.icon(fileName: "error_indicator", size: 32)
                RiveAnimationView.icon(fileName: "loading_spinner", size: 32)
            }
            
            // 自定义动画
            RiveAnimationView(
                fileName: "process_indicator",
                autoPlay: true,
                loop: true,
                fit: .contain,
                alignment: .center
            )
            .frame(height: 100)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
        }
        .padding()
        .frame(width: 400, height: 500)
    }
}
#endif