//
//  MenuBarView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct MenuBarView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var homeViewModel: HomeViewModel
    
    // MARK: - Initialization
    init() {
        self._homeViewModel = StateObject(wrappedValue: DIContainer.shared.homeViewModel)
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            // 标题
            Text("MCP Copilot")
                .labelText()
                .padding(.horizontal, Spacing.sm)
            
            Divider()
            
            // 活跃场景
            if homeViewModel.hasActiveScenes {
                Text("运行中的场景")
                    .captionText()
                    .padding(.horizontal, Spacing.sm)
                
                ForEach(homeViewModel.activeScenes, id: \.id) { scene in
                    menuSceneRow(scene)
                }
                
                Divider()
            }
            
            // 快速操作
            quickActions
            
            Divider()
            
            // 应用控制
            appControls
        }
        .frame(width: 250)
        .onAppear {
            homeViewModel.loadScenes()
        }
    }
    
    // MARK: - Menu Scene Row
    private func menuSceneRow(_ scene: MCPScene) -> some View {
        HStack(spacing: Spacing.sm) {
            Circle()
                .fill(Color.forProcessStatus(homeViewModel.getProcessStatus(for: scene)))
                .frame(width: 8, height: 8)
            
            Text(scene.name)
                .bodyText()
                .lineLimit(1)
            
            Spacer()
            
            Text(":\(scene.port)")
                .captionText()
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Spacing.sm)
        .padding(.vertical, Spacing.xs)
        .contentShape(Rectangle())
        .onTapGesture {
            // TODO: 打开主窗口并选择该场景
        }
        .contextMenu {
            Button("停止", systemImage: "stop.fill") {
                homeViewModel.stopScene(scene)
            }
            
            Button("重启", systemImage: "arrow.clockwise") {
                homeViewModel.restartScene(scene)
            }
        }
    }
    
    // MARK: - Quick Actions
    private var quickActions: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Button("打开主窗口", systemImage: "app.dashed") {
                // TODO: 打开主窗口
                NSApp.activate(ignoringOtherApps: true)
            }
            
            Button("创建新场景", systemImage: "plus") {
                // TODO: 打开创建场景窗口
            }
            
            if homeViewModel.hasActiveScenes {
                Button("停止所有场景", systemImage: "stop.fill") {
                    Task {
                        for scene in homeViewModel.activeScenes {
                            homeViewModel.stopScene(scene)
                        }
                    }
                }
                .foregroundColor(.statusError)
            }
        }
        .buttonStyle(.borderless)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, Spacing.sm)
    }
    
    // MARK: - App Controls
    private var appControls: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Button("设置", systemImage: "gear") {
                // TODO: 打开设置窗口
            }
            
            Button("查看日志", systemImage: "doc.text") {
                // TODO: 打开日志窗口
            }
            
            Button("关于", systemImage: "info.circle") {
                // TODO: 显示关于信息
            }
            
            Divider()
            
            Button("退出", systemImage: "power") {
                NSApplication.shared.terminate(nil)
            }
            .foregroundColor(.statusError)
        }
        .buttonStyle(.borderless)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, Spacing.sm)
    }
}

// MARK: - Preview
#Preview {
    MenuBarView()
        .environment(\.diContainer, DIContainer.shared)
}
