//
//  SceneDetailView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct SceneDetailView: View {
    
    // MARK: - Properties
    let scene: MCPScene
    @Environment(\.diContainer) private var diContainer
    
    // MARK: - Body
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: Spacing.Content.sectionSpacing) {
                // 场景标题和状态
                sceneHeader
                
                // 基本信息
                basicInfoSection
                
                // 服务器列表
                serversSection
                
                // 环境变量
                environmentSection
                
                // 监控信息
                monitoringSection
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    // MARK: - Scene Header
    private var sceneHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(scene.name)
                    .pageTitle()
                
                HStack(spacing: Spacing.sm) {
                    statusBadge
                    
                    Text("端口: \(scene.port)")
                        .captionText()
                    
                    if !scene.servers.isEmpty {
                        Text("•")
                            .captionText()
                        
                        Text("\(scene.servers.count) 个服务")
                            .captionText()
                    }
                }
            }
            
            Spacer()
            
            actionButtons
        }
    }
    
    // MARK: - Status Badge
    private var statusBadge: some View {
        HStack(spacing: Spacing.xs) {
            Circle()
                .fill(scene.isActive ? Color.statusSuccess : Color.textSecondary)
                .frame(width: 8, height: 8)
            
            Text(scene.isActive ? "运行中" : "已停止")
                .captionText()
        }
        .padding(.horizontal, Spacing.sm)
        .padding(.vertical, Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundSecondary)
        )
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: Spacing.sm) {
            if !scene.isActive {
                Button("启动", systemImage: "play.fill") {
                    // TODO: 启动场景
                }
                .buttonStyle(.borderedProminent)
            } else {
                Button("停止", systemImage: "stop.fill") {
                    // TODO: 停止场景
                }
                .buttonStyle(.bordered)
                .foregroundColor(.statusError)
                
                Button("重启", systemImage: "arrow.clockwise") {
                    // TODO: 重启场景
                }
                .buttonStyle(.bordered)
            }
            
            Button("编辑", systemImage: "pencil") {
                // TODO: 编辑场景
            }
            .buttonStyle(.bordered)
        }
    }
    
    // MARK: - Basic Info Section
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("基本信息")
                .sectionTitle()
            
            VStack(alignment: .leading, spacing: Spacing.sm) {
                infoRow("名称", scene.name)
                infoRow("端口", "\(scene.port)")
                infoRow("状态", scene.isActive ? "运行中" : "已停止")
                infoRow("创建时间", formatDate(scene.createdAt))
                infoRow("更新时间", formatDate(scene.updatedAt))
            }
            .cardPadding()
            .background(
                RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                            .stroke(Color.cardBorder, lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Servers Section
    private var serversSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Text("MCP 服务器")
                    .sectionTitle()
                
                Spacer()
                
                Button("添加服务器", systemImage: "plus") {
                    // TODO: 添加服务器
                }
                .buttonStyle(.bordered)
            }
            
            if scene.servers.isEmpty {
                emptyServersView
            } else {
                LazyVStack(spacing: Spacing.sm) {
                    ForEach(scene.servers, id: \.id) { server in
                        ServerRowView(server: server)
                    }
                }
            }
        }
    }
    
    // MARK: - Environment Section
    private var environmentSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Text("环境变量")
                    .sectionTitle()
                
                Spacer()
                
                Button("编辑", systemImage: "pencil") {
                    // TODO: 编辑环境变量
                }
                .buttonStyle(.bordered)
            }
            
            if scene.environment.isEmpty {
                emptyEnvironmentView
            } else {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    ForEach(Array(scene.environment.keys.sorted()), id: \.self) { key in
                        if let value = scene.environment[key] {
                            environmentRow(key: key, value: value)
                        }
                    }
                }
                .cardPadding()
                .background(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .fill(Color.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                .stroke(Color.cardBorder, lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - Monitoring Section
    private var monitoringSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("监控信息")
                .sectionTitle()
            
            // TODO: 添加监控图表和指标
            Text("监控功能开发中...")
                .captionText()
                .frame(maxWidth: .infinity)
                .cardPadding()
                .background(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .fill(Color.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                                .stroke(Color.cardBorder, lineWidth: 1)
                        )
                )
        }
    }
    
    // MARK: - Helper Views
    private func infoRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label)
                .labelText()
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .bodyText()
            
            Spacer()
        }
    }
    
    private func environmentRow(key: String, value: String) -> some View {
        HStack {
            Text(key)
                .codeText()
                .foregroundColor(.primaryBlue)
            
            Text("=")
                .captionText()
            
            Text(value)
                .codeText()
            
            Spacer()
        }
    }
    
    private var emptyServersView: some View {
        VStack(spacing: Spacing.md) {
            Image(systemName: "server.rack")
                .font(.system(size: 32))
                .foregroundColor(.textSecondary)
            
            Text("暂无 MCP 服务器")
                .bodyText()
            
            Text("点击上方的 + 按钮添加第一个服务器")
                .captionText()
        }
        .frame(maxWidth: .infinity)
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
    }
    
    private var emptyEnvironmentView: some View {
        VStack(spacing: Spacing.md) {
            Image(systemName: "gear")
                .font(.system(size: 32))
                .foregroundColor(.textSecondary)
            
            Text("暂无环境变量")
                .bodyText()
            
            Text("点击编辑按钮添加环境变量")
                .captionText()
        }
        .frame(maxWidth: .infinity)
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - Server Row View
private struct ServerRowView: View {
    let server: MCPServer
    
    var body: some View {
        HStack(spacing: Spacing.sm) {
            // 服务器类型图标
            Image(systemName: server.type == .stdio ? "terminal" : "network")
                .font(.system(size: Spacing.Icon.medium))
                .foregroundColor(.primaryBlue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(server.name)
                    .labelText()
                
                Text(server.namespace)
                    .captionText()
            }
            
            Spacer()
            
            // 状态指示器
            Circle()
                .fill(server.isRunning ? Color.statusSuccess : Color.textSecondary)
                .frame(width: 8, height: 8)
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(Color.backgroundSecondary)
        )
    }
}

// MARK: - Preview
#Preview {
    let scene = MCPScene(name: "开发环境", port: 7700, isActive: true)
    scene.environment = ["API_KEY": "sk-xxx", "DEBUG": "true"]
    
    return SceneDetailView(scene: scene)
        .environment(\.diContainer, DIContainer.shared)
        .padding()
}
