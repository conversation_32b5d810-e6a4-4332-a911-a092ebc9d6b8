//
//  MacOSComponents.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS specific UI components
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Toolbar Components

/// macOS 风格的工具栏
struct MacOSToolbar: View {
    let title: String
    let leading: AnyView?
    let trailing: AnyView?
    
    init(
        title: String,
        @ViewBuilder leading: () -> AnyView = { AnyView(EmptyView()) },
        @ViewBuilder trailing: () -> AnyView = { AnyView(EmptyView()) }
    ) {
        self.title = title
        self.leading = leading()
        self.trailing = trailing()
    }
    
    var body: some View {
        HStack {
            leading
            
            Spacer()
            
            Text(title)
                .font(DesignTokens.typography.headline.value)
                .foregroundColor(DesignTokens.color.textPrimary.value)
            
            Spacer()
            
            trailing
        }
        .padding(.horizontal, DesignTokens.spacing.lg.value)
        .padding(.vertical, DesignTokens.spacing.md.value)
        .background(DesignTokens.color.surfaceToolbar.value)
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(DesignTokens.color.borderDivider.value),
            alignment: .bottom
        )
    }
}

// MARK: - Sidebar Components

/// macOS 风格的侧边栏
struct MacOSSidebar<Content: View>: View {
    let content: Content
    let width: CGFloat
    let isCollapsed: Bool
    
    init(
        width: CGFloat = 280,
        isCollapsed: Bool = false,
        @ViewBuilder content: () -> Content
    ) {
        self.width = width
        self.isCollapsed = isCollapsed
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            content
        }
        .frame(width: isCollapsed ? 0 : width)
        .background(DesignTokens.color.surfaceSidebar.value)
        .overlay(
            Rectangle()
                .frame(width: 1)
                .foregroundColor(DesignTokens.color.borderDivider.value),
            alignment: .trailing
        )
        .animation(DesignTokens.animation.sidebarToggle.value, value: isCollapsed)
    }
}

/// 侧边栏项目
struct SidebarItem: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignTokens.spacing.sm.value) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? DesignTokens.color.primary.value : DesignTokens.color.textSecondary.value)
                    .frame(width: 20)
                
                Text(title)
                    .font(DesignTokens.typography.body.value)
                    .foregroundColor(isSelected ? DesignTokens.color.textPrimary.value : DesignTokens.color.textSecondary.value)
                
                Spacer()
            }
            .padding(.horizontal, DesignTokens.spacing.md.value)
            .padding(.vertical, DesignTokens.spacing.sm.value)
            .background(
                RoundedRectangle(cornerRadius: DesignTokens.borderRadius.sm.value)
                    .fill(isSelected ? DesignTokens.color.surfaceSelection.value : Color.clear)
            )
            .padding(.horizontal, DesignTokens.spacing.sm.value)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            // macOS 悬停效果
            if hovering && !isSelected {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
        }
    }
}

// MARK: - Card Components

/// macOS 风格的卡片
struct MacOSCard<Content: View>: View {
    let content: Content
    let padding: EdgeInsets
    let cornerRadius: CGFloat
    
    init(
        padding: EdgeInsets = DesignTokens.spacing.cardPadding.value,
        cornerRadius: CGFloat = DesignTokens.borderRadius.card.value,
        @ViewBuilder content: () -> Content
    ) {
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            content
        }
        .padding(padding)
        .background(DesignTokens.color.surfaceCard.value)
        .cornerRadius(cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(DesignTokens.color.borderPrimary.value, lineWidth: 1)
        )
        .shadow(
            color: DesignTokens.shadow.sm.value.color,
            radius: DesignTokens.shadow.sm.value.radius,
            x: DesignTokens.shadow.sm.value.offset.width,
            y: DesignTokens.shadow.sm.value.offset.height
        )
    }
}

// MARK: - Button Components

/// macOS 风格的按钮
struct MacOSButton: View {
    let title: String
    let icon: String?
    let style: ButtonStyle
    let action: () -> Void
    
    enum ButtonStyle {
        case primary
        case secondary
        case danger
        case success
        
        var backgroundColor: Color {
            switch self {
            case .primary:
                return DesignTokens.color.primary.value
            case .secondary:
                return DesignTokens.color.backgroundSecondary.value
            case .danger:
                return DesignTokens.color.error.value
            case .success:
                return DesignTokens.color.success.value
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .danger, .success:
                return .white
            case .secondary:
                return DesignTokens.color.textPrimary.value
            }
        }
    }
    
    init(
        title: String,
        icon: String? = nil,
        style: ButtonStyle = .primary,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignTokens.spacing.sm.value) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 14))
                }
                
                Text(title)
                    .font(DesignTokens.typography.body.value)
            }
            .foregroundColor(style.foregroundColor)
            .padding(DesignTokens.spacing.buttonPadding.value)
            .background(
                RoundedRectangle(cornerRadius: DesignTokens.borderRadius.button.value)
                    .fill(style.backgroundColor)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            if hovering {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
        }
    }
}

// MARK: - Status Indicator

/// 状态指示器
struct StatusIndicator: View {
    let status: ProcessStatus
    let animated: Bool
    
    init(status: ProcessStatus, animated: Bool = true) {
        self.status = status
        self.animated = animated
    }
    
    var body: some View {
        Circle()
            .fill(Color.forProcessStatus(status))
            .frame(width: 12, height: 12)
            .opacity(animated && status == .starting ? 0.5 : 1.0)
            .animation(
                animated && status == .starting ? 
                    Animation.easeInOut(duration: 0.8).repeatForever() : 
                    nil,
                value: animated
            )
    }
}

// MARK: - Progress Components

/// macOS 风格的进度条
struct MacOSProgressBar: View {
    let progress: Double
    let showPercentage: Bool
    
    init(progress: Double, showPercentage: Bool = false) {
        self.progress = progress
        self.showPercentage = showPercentage
    }
    
    var body: some View {
        VStack(spacing: DesignTokens.spacing.xs.value) {
            HStack {
                if showPercentage {
                    Spacer()
                    Text("\(Int(progress * 100))%")
                        .font(DesignTokens.typography.caption.value)
                        .foregroundColor(DesignTokens.color.textSecondary.value)
                }
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(DesignTokens.color.backgroundSecondary.value)
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(DesignTokens.color.primary.value)
                        .frame(
                            width: geometry.size.width * CGFloat(progress),
                            height: 6
                        )
                        .cornerRadius(3)
                        .animation(DesignTokens.animation.standard.value, value: progress)
                }
            }
            .frame(height: 6)
        }
    }
}

// MARK: - Search Components

/// macOS 风格的搜索框
struct MacOSSearchField: View {
    @Binding var text: String
    let placeholder: String
    let onSearchAction: (String) -> Void
    
    init(
        text: Binding<String>,
        placeholder: String = "搜索...",
        onSearchAction: @escaping (String) -> Void = { _ in }
    ) {
        self._text = text
        self.placeholder = placeholder
        self.onSearchAction = onSearchAction
    }
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(DesignTokens.color.textSecondary.value)
                .padding(.leading, DesignTokens.spacing.sm.value)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(PlainTextFieldStyle())
                .font(DesignTokens.typography.body.value)
                .onSubmit {
                    onSearchAction(text)
                }
            
            if !text.isEmpty {
                Button(action: { text = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(DesignTokens.color.textSecondary.value)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.trailing, DesignTokens.spacing.sm.value)
            }
        }
        .padding(.vertical, DesignTokens.spacing.sm.value)
        .background(DesignTokens.color.backgroundSecondary.value)
        .cornerRadius(DesignTokens.borderRadius.sm.value)
        .overlay(
            RoundedRectangle(cornerRadius: DesignTokens.borderRadius.sm.value)
                .stroke(DesignTokens.color.borderPrimary.value, lineWidth: 1)
        )
    }
}

// MARK: - Floating Panel

/// 浮动面板
struct FloatingPanel<Content: View>: View {
    let content: Content
    let isVisible: Bool
    let onDismiss: () -> Void
    
    init(
        isVisible: Bool,
        onDismiss: @escaping () -> Void,
        @ViewBuilder content: () -> Content
    ) {
        self.isVisible = isVisible
        self.onDismiss = onDismiss
        self.content = content()
    }
    
    var body: some View {
        if isVisible {
            ZStack {
                // 背景遮罩
                Color.black.opacity(0.2)
                    .ignoresSafeArea()
                    .onTapGesture {
                        onDismiss()
                    }
                
                // 面板内容
                VStack(spacing: 0) {
                    content
                }
                .background(DesignTokens.color.surfaceFloating.value)
                .cornerRadius(DesignTokens.borderRadius.lg.value)
                .shadow(
                    color: DesignTokens.shadow.floatingPanel.value.color,
                    radius: DesignTokens.shadow.floatingPanel.value.radius,
                    x: DesignTokens.shadow.floatingPanel.value.offset.width,
                    y: DesignTokens.shadow.floatingPanel.value.offset.height
                )
                .scaleEffect(isVisible ? 1.0 : 0.8)
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(DesignTokens.animation.spring.value, value: isVisible)
            }
        }
    }
}

// MARK: - Log Entry View

/// 日志条目视图
struct LogEntryView: View {
    let entry: LogEntry
    let isExpanded: Bool
    let onToggle: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignTokens.spacing.xs.value) {
            HStack {
                // 时间戳
                Text(formatTimestamp(entry.timestamp))
                    .font(DesignTokens.typography.caption.value)
                    .foregroundColor(DesignTokens.color.textSecondary.value)
                    .frame(width: 80, alignment: .leading)
                
                // 级别
                Text(entry.level.displayName)
                    .font(DesignTokens.typography.caption.value)
                    .foregroundColor(Color.forLogLevel(entry.level))
                    .frame(width: 60, alignment: .leading)
                
                // 消息
                Text(entry.message)
                    .font(DesignTokens.typography.body.value)
                    .foregroundColor(DesignTokens.color.textPrimary.value)
                    .lineLimit(isExpanded ? nil : 1)
                
                Spacer()
                
                // 展开/收起按钮
                if entry.message.contains("\n") {
                    Button(action: onToggle) {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 12))
                            .foregroundColor(DesignTokens.color.textSecondary.value)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 扩展信息
            if isExpanded && !entry.details.isEmpty {
                Text(entry.details)
                    .font(DesignTokens.typography.caption.value)
                    .foregroundColor(DesignTokens.color.textSecondary.value)
                    .padding(.leading, 140)
            }
        }
        .padding(.vertical, DesignTokens.spacing.xs.value)
        .padding(.horizontal, DesignTokens.spacing.sm.value)
        .background(entry.level == .error ? DesignTokens.color.error.value.opacity(0.1) : Color.clear)
        .cornerRadius(DesignTokens.borderRadius.sm.value)
    }
    
    private func formatTimestamp(_ timestamp: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: timestamp)
    }
}

// MARK: - Preview Support

#if DEBUG
struct MacOSComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            MacOSToolbar(title: "MCP Copilot") {
                AnyView(
                    MacOSButton(title: "Back", icon: "chevron.left", style: .secondary) {
                        print("Back tapped")
                    }
                )
            } trailing: {
                AnyView(
                    MacOSButton(title: "Settings", icon: "gear", style: .secondary) {
                        print("Settings tapped")
                    }
                )
            }
            
            HStack {
                MacOSSidebar {
                    VStack(spacing: 4) {
                        SidebarItem(title: "Home", icon: "house", isSelected: true) {
                            print("Home selected")
                        }
                        SidebarItem(title: "Monitor", icon: "chart.bar", isSelected: false) {
                            print("Monitor selected")
                        }
                        SidebarItem(title: "Settings", icon: "gear", isSelected: false) {
                            print("Settings selected")
                        }
                    }
                    .padding(.top, DesignTokens.spacing.md.value)
                }
                
                VStack(spacing: 16) {
                    MacOSCard {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("System Status")
                                .font(DesignTokens.typography.headline.value)
                            
                            HStack {
                                StatusIndicator(status: .running)
                                Text("All services running")
                                    .font(DesignTokens.typography.body.value)
                            }
                            
                            MacOSProgressBar(progress: 0.75, showPercentage: true)
                        }
                    }
                    
                    MacOSSearchField(text: .constant("")) { query in
                        print("Search: \(query)")
                    }
                }
                .padding()
            }
        }
        .frame(width: 800, height: 600)
        .themedAppearance()
    }
}
#endif