//
//  SceneRowView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct SceneRowView: View {
    
    // MARK: - Properties
    let scene: MCPScene
    let status: ProcessStatus
    let isSelected: Bool
    
    let onSelect: () -> Void
    let onStart: () -> Void
    let onStop: () -> Void
    let onRestart: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteAlert = false
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: Spacing.sm) {
            // 状态指示器
            statusIndicator
            
            // 场景信息
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(scene.name)
                    .labelText()
                    .lineLimit(1)
                
                HStack(spacing: Spacing.xs) {
                    Text("端口: \(scene.port)")
                        .captionText()
                    
                    if !scene.servers.isEmpty {
                        Text("•")
                            .captionText()
                        
                        Text("\(scene.servers.count) 个服务")
                            .captionText()
                    }
                }
            }
            
            Spacer()
            
            // 操作按钮
            actionButtons
        }
        .cardPadding()
        .background(
            RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                .fill(isSelected ? Color.sidebarSelection : Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Spacing.Card.cornerRadius)
                        .stroke(Color.cardBorder, lineWidth: 1)
                )
        )
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .contextMenu {
            contextMenuItems
        }
        .alert("确认删除", isPresented: $showingDeleteAlert) {
            Button("删除", role: .destructive) {
                onDelete()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("确定要删除场景 \"\(scene.name)\" 吗？此操作无法撤销。")
        }
    }
    
    // MARK: - Status Indicator
    private var statusIndicator: some View {
        Circle()
            .fill(Color.forProcessStatus(status))
            .frame(width: Spacing.StatusIndicator.size, height: Spacing.StatusIndicator.size)
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 1)
            )
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: Spacing.xs) {
            if canStart {
                Button(action: onStart) {
                    Image(systemName: "play.fill")
                        .font(.system(size: Spacing.Icon.small))
                }
                .buttonStyle(.borderless)
                .foregroundColor(.statusSuccess)
                .help("启动场景")
            }
            
            if canStop {
                Button(action: onStop) {
                    Image(systemName: "stop.fill")
                        .font(.system(size: Spacing.Icon.small))
                }
                .buttonStyle(.borderless)
                .foregroundColor(.statusError)
                .help("停止场景")
            }
            
            if canRestart {
                Button(action: onRestart) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: Spacing.Icon.small))
                }
                .buttonStyle(.borderless)
                .foregroundColor(.statusWarning)
                .help("重启场景")
            }
            
            Menu {
                contextMenuItems
            } label: {
                Image(systemName: "ellipsis")
                    .font(.system(size: Spacing.Icon.small))
            }
            .buttonStyle(.borderless)
            .foregroundColor(.textSecondary)
            .help("更多选项")
        }
    }
    
    // MARK: - Context Menu Items
    private var contextMenuItems: some View {
        Group {
            Button("编辑", systemImage: "pencil") {
                onEdit()
            }
            
            Divider()
            
            if canStart {
                Button("启动", systemImage: "play.fill") {
                    onStart()
                }
            }
            
            if canStop {
                Button("停止", systemImage: "stop.fill") {
                    onStop()
                }
            }
            
            if canRestart {
                Button("重启", systemImage: "arrow.clockwise") {
                    onRestart()
                }
            }
            
            Divider()
            
            Button("删除", systemImage: "trash", role: .destructive) {
                showingDeleteAlert = true
            }
        }
    }
    
    // MARK: - Computed Properties
    private var canStart: Bool {
        return !scene.isActive && status == .stopped
    }
    
    private var canStop: Bool {
        return scene.isActive && status == .running
    }
    
    private var canRestart: Bool {
        return scene.isActive
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: Spacing.sm) {
        SceneRowView(
            scene: MCPScene(name: "开发环境", port: 7700, isActive: true),
            status: .running,
            isSelected: false,
            onSelect: { },
            onStart: { },
            onStop: { },
            onRestart: { },
            onEdit: { },
            onDelete: { }
        )
        
        SceneRowView(
            scene: MCPScene(name: "测试环境", port: 7701, isActive: false),
            status: .stopped,
            isSelected: true,
            onSelect: { },
            onStart: { },
            onStop: { },
            onRestart: { },
            onEdit: { },
            onDelete: { }
        )
        
        SceneRowView(
            scene: MCPScene(name: "生产环境", port: 7702, isActive: true),
            status: .error,
            isSelected: false,
            onSelect: { },
            onStart: { },
            onStop: { },
            onRestart: { },
            onEdit: { },
            onDelete: { }
        )
    }
    .padding()
    .frame(width: 300)
}
