//
//  FloatingPositionHelper.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI
import AppKit

/// 浮动窗口和面板位置管理辅助工具
struct FloatingPositionHelper {
    
    // MARK: - Position Types
    enum Position {
        case topLeft
        case topRight
        case bottomLeft
        case bottomRight
        case center
        case custom(CGPoint)
    }
    
    enum Anchor {
        case topLeading
        case topTrailing
        case bottomLeading
        case bottomTrailing
        case center
    }
    
    // MARK: - Position Calculation
    
    /// 计算浮动窗口的位置
    static func calculatePosition(
        for size: CGSize,
        position: Position,
        offset: CGSize = .zero,
        relativeTo window: NSWindow? = nil
    ) -> CGPoint {
        let referenceFrame = window?.frame ?? NSScreen.main?.visibleFrame ?? .zero
        
        let basePoint: CGPoint
        
        switch position {
        case .topLeft:
            basePoint = CGPoint(x: referenceFrame.minX, y: referenceFrame.maxY - size.height)
        case .topRight:
            basePoint = CGPoint(x: referenceFrame.maxX - size.width, y: referenceFrame.maxY - size.height)
        case .bottomLeft:
            basePoint = CGPoint(x: referenceFrame.minX, y: referenceFrame.minY)
        case .bottomRight:
            basePoint = CGPoint(x: referenceFrame.maxX - size.width, y: referenceFrame.minY)
        case .center:
            basePoint = CGPoint(
                x: referenceFrame.midX - size.width / 2,
                y: referenceFrame.midY - size.height / 2
            )
        case .custom(let point):
            basePoint = point
        }
        
        return CGPoint(
            x: basePoint.x + offset.width,
            y: basePoint.y + offset.height
        )
    }
    
    /// 计算相对于父视图的锚点位置
    static func anchorPosition(
        for anchor: Anchor,
        in frame: CGRect,
        size: CGSize,
        padding: CGFloat = 8
    ) -> CGPoint {
        switch anchor {
        case .topLeading:
            return CGPoint(x: frame.minX + padding, y: frame.maxY - size.height - padding)
        case .topTrailing:
            return CGPoint(x: frame.maxX - size.width - padding, y: frame.maxY - size.height - padding)
        case .bottomLeading:
            return CGPoint(x: frame.minX + padding, y: frame.minY + padding)
        case .bottomTrailing:
            return CGPoint(x: frame.maxX - size.width - padding, y: frame.minY + padding)
        case .center:
            return CGPoint(
                x: frame.midX - size.width / 2,
                y: frame.midY - size.height / 2
            )
        }
    }
    
    // MARK: - Animation Helpers
    
    /// 创建浮动动画
    static func floatingAnimation(duration: Double = 2.0, amplitude: CGFloat = 10.0) -> Animation {
        return Animation.easeInOut(duration: duration)
            .repeatForever(autoreverses: true)
    }
    
    /// 创建弹跳动画
    static func bounceAnimation(duration: Double = 0.6) -> Animation {
        return Animation.interpolatingSpring(
            mass: 1.0,
            stiffness: 100.0,
            damping: 10.0,
            initialVelocity: 0
        )
    }
    
    /// 创建淡入淡出动画
    static func fadeAnimation(duration: Double = 0.3) -> Animation {
        return Animation.easeInOut(duration: duration)
    }
}

// MARK: - Floating Panel Modifier
struct FloatingPanelModifier: ViewModifier {
    let position: FloatingPositionHelper.Position
    let offset: CGSize
    let isVisible: Bool
    let animation: Animation?
    
    init(
        position: FloatingPositionHelper.Position = .center,
        offset: CGSize = .zero,
        isVisible: Bool = true,
        animation: Animation? = FloatingPositionHelper.fadeAnimation()
    ) {
        self.position = position
        self.offset = offset
        self.isVisible = isVisible
        self.animation = animation
    }
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1.0 : 0.0)
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .animation(animation, value: isVisible)
    }
}

extension View {
    func floatingPanel(
        position: FloatingPositionHelper.Position = .center,
        offset: CGSize = .zero,
        isVisible: Bool = true,
        animation: Animation? = FloatingPositionHelper.fadeAnimation()
    ) -> some View {
        self.modifier(FloatingPanelModifier(
            position: position,
            offset: offset,
            isVisible: isVisible,
            animation: animation
        ))
    }
}

// MARK: - Draggable Modifier
struct DraggableModifier: ViewModifier {
    @State private var dragOffset = CGSize.zero
    @State private var isDragging = false
    
    let onDragChanged: ((CGSize) -> Void)?
    let onDragEnded: ((CGSize) -> Void)?
    
    init(
        onDragChanged: ((CGSize) -> Void)? = nil,
        onDragEnded: ((CGSize) -> Void)? = nil
    ) {
        self.onDragChanged = onDragChanged
        self.onDragEnded = onDragEnded
    }
    
    func body(content: Content) -> some View {
        content
            .offset(dragOffset)
            .scaleEffect(isDragging ? 1.05 : 1.0)
            .animation(.spring(response: 0.3), value: isDragging)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        dragOffset = value.translation
                        isDragging = true
                        onDragChanged?(value.translation)
                    }
                    .onEnded { value in
                        isDragging = false
                        onDragEnded?(value.translation)
                        
                        // 可选：自动回弹到原位置
                        withAnimation(.spring()) {
                            dragOffset = .zero
                        }
                    }
            )
    }
}

extension View {
    func draggable(
        onDragChanged: ((CGSize) -> Void)? = nil,
        onDragEnded: ((CGSize) -> Void)? = nil
    ) -> some View {
        self.modifier(DraggableModifier(
            onDragChanged: onDragChanged,
            onDragEnded: onDragEnded
        ))
    }
}

// MARK: - Floating Button
struct FloatingButton: View {
    let systemImage: String
    let action: () -> Void
    let position: FloatingPositionHelper.Position
    let size: CGFloat
    let backgroundColor: Color
    let foregroundColor: Color
    
    @State private var isHovered = false
    
    init(
        systemImage: String,
        position: FloatingPositionHelper.Position = .bottomRight,
        size: CGFloat = 56,
        backgroundColor: Color = .primaryBlue,
        foregroundColor: Color = .white,
        action: @escaping () -> Void
    ) {
        self.systemImage = systemImage
        self.position = position
        self.size = size
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: systemImage)
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundColor(foregroundColor)
                .frame(width: size, height: size)
                .background(
                    Circle()
                        .fill(backgroundColor)
                        .shadow(
                            color: .black.opacity(0.2),
                            radius: isHovered ? 8 : 4,
                            x: 0,
                            y: isHovered ? 4 : 2
                        )
                )
        }
        .buttonStyle(.plain)
        .scaleEffect(isHovered ? 1.1 : 1.0)
        .animation(.spring(response: 0.3), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
    }
}
