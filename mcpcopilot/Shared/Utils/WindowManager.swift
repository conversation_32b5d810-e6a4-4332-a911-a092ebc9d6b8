//
//  WindowManager.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/15.
//  macOS specific window management utilities
//

import SwiftUI

#if canImport(AppKit)
import AppKit
#endif

// MARK: - Window Manager

/// macOS 窗口管理器 - 处理窗口生命周期和状态
@MainActor
class WindowManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = WindowManager()
    
    // MARK: - Published Properties
    @Published var isMainWindowVisible: Bool = true
    @Published var windowFrames: [String: NSRect] = [:]
    @Published var windowStates: [String: WindowState] = [:]
    
    // MARK: - Window State
    enum WindowState {
        case normal
        case minimized
        case maximized
        case fullscreen
        case hidden
    }
    
    // MARK: - Window Configuration
    struct WindowConfiguration {
        let identifier: String
        let title: String
        let minSize: NSSize
        let maxSize: NSSize?
        let isResizable: Bool
        let hasTitleBar: Bool
        let hasCloseButton: Bool
        let hasMinimizeButton: Bool
        let hasMaximizeButton: Bool
        let level: NSWindow.Level
        
        static let `default` = WindowConfiguration(
            identifier: "main",
            title: "MCP Copilot",
            minSize: NSSize(width: 800, height: 600),
            maxSize: nil,
            isResizable: true,
            hasTitleBar: true,
            hasCloseButton: true,
            hasMinimizeButton: true,
            hasMaximizeButton: true,
            level: .normal
        )
        
        static let floating = WindowConfiguration(
            identifier: "floating",
            title: "Floating Panel",
            minSize: NSSize(width: 300, height: 200),
            maxSize: NSSize(width: 500, height: 400),
            isResizable: true,
            hasTitleBar: false,
            hasCloseButton: false,
            hasMinimizeButton: false,
            hasMaximizeButton: false,
            level: .floating
        )
        
        static let debug = WindowConfiguration(
            identifier: "debug",
            title: "Debug Console",
            minSize: NSSize(width: 600, height: 400),
            maxSize: nil,
            isResizable: true,
            hasTitleBar: true,
            hasCloseButton: true,
            hasMinimizeButton: true,
            hasMaximizeButton: true,
            level: .normal
        )
    }
    
    // MARK: - Private Properties
    private var windows: [String: NSWindow] = [:]
    private var windowObservers: [String: [NSObjectProtocol]] = [:]
    
    private init() {
        setupApplicationObservers()
    }
    
    deinit {
        cleanup()
    }
    
    // MARK: - Public Methods
    
    /// 创建新窗口
    func createWindow(
        with configuration: WindowConfiguration,
        contentView: some View,
        onClose: (() -> Void)? = nil
    ) -> NSWindow? {
        
        #if canImport(AppKit)
        let window = NSWindow(
            contentRect: NSRect(
                x: 0, y: 0,
                width: configuration.minSize.width,
                height: configuration.minSize.height
            ),
            styleMask: createStyleMask(for: configuration),
            backing: .buffered,
            defer: false
        )
        
        // 配置窗口属性
        window.title = configuration.title
        window.minSize = configuration.minSize
        if let maxSize = configuration.maxSize {
            window.maxSize = maxSize
        }
        window.level = configuration.level
        window.isReleasedWhenClosed = false
        
        // 设置内容视图
        let hostingView = NSHostingView(rootView: contentView)
        window.contentView = hostingView
        
        // 居中显示
        window.center()
        
        // 存储窗口
        windows[configuration.identifier] = window
        windowStates[configuration.identifier] = .normal
        
        // 设置窗口观察者
        setupWindowObservers(for: window, identifier: configuration.identifier, onClose: onClose)
        
        return window
        #else
        return nil
        #endif
    }
    
    /// 显示窗口
    func showWindow(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.makeKeyAndOrderFront(nil)
        windowStates[identifier] = .normal
        #endif
    }
    
    /// 隐藏窗口
    func hideWindow(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.orderOut(nil)
        windowStates[identifier] = .hidden
        #endif
    }
    
    /// 关闭窗口
    func closeWindow(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.close()
        #endif
        
        // 清理资源
        cleanupWindow(identifier: identifier)
    }
    
    /// 最小化窗口
    func minimizeWindow(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.miniaturize(nil)
        windowStates[identifier] = .minimized
        #endif
    }
    
    /// 恢复窗口
    func restoreWindow(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.deminiaturize(nil)
        windowStates[identifier] = .normal
        #endif
    }
    
    /// 切换全屏
    func toggleFullscreen(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        window.toggleFullScreen(nil)
        #endif
    }
    
    /// 获取窗口状态
    func getWindowState(identifier: String) -> WindowState? {
        return windowStates[identifier]
    }
    
    /// 获取窗口框架
    func getWindowFrame(identifier: String) -> NSRect? {
        return windowFrames[identifier]
    }
    
    /// 保存窗口位置
    func saveWindowPosition(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        let frame = window.frame
        windowFrames[identifier] = frame
        
        // 保存到 UserDefaults
        let frameData = NSStringFromRect(frame)
        UserDefaults.standard.set(frameData, forKey: "window_frame_\(identifier)")
        #endif
    }
    
    /// 恢复窗口位置
    func restoreWindowPosition(identifier: String) {
        guard let window = windows[identifier] else { return }
        
        #if canImport(AppKit)
        let key = "window_frame_\(identifier)"
        if let frameString = UserDefaults.standard.string(forKey: key) {
            let frame = NSRectFromString(frameString)
            if !frame.isEmpty {
                window.setFrame(frame, display: true)
                windowFrames[identifier] = frame
            }
        }
        #endif
    }
    
    // MARK: - Private Methods
    
    private func createStyleMask(for configuration: WindowConfiguration) -> NSWindow.StyleMask {
        #if canImport(AppKit)
        var styleMask: NSWindow.StyleMask = []
        
        if configuration.hasTitleBar {
            styleMask.insert(.titled)
        }
        if configuration.hasCloseButton {
            styleMask.insert(.closable)
        }
        if configuration.hasMinimizeButton {
            styleMask.insert(.miniaturizable)
        }
        if configuration.hasMaximizeButton {
            styleMask.insert(.resizable)
        }
        if configuration.isResizable {
            styleMask.insert(.resizable)
        }
        
        return styleMask
        #else
        return []
        #endif
    }
    
    private func setupWindowObservers(for window: NSWindow, identifier: String, onClose: (() -> Void)?) {
        #if canImport(AppKit)
        var observers: [NSObjectProtocol] = []
        
        // 窗口关闭观察者
        let closeObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.willCloseNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            onClose?()
            self?.cleanupWindow(identifier: identifier)
        }
        observers.append(closeObserver)
        
        // 窗口移动观察者
        let moveObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didMoveNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowFrames[identifier] = window.frame
        }
        observers.append(moveObserver)
        
        // 窗口大小改变观察者
        let resizeObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didResizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowFrames[identifier] = window.frame
        }
        observers.append(resizeObserver)
        
        // 窗口最小化观察者
        let minimizeObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didMiniaturizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowStates[identifier] = .minimized
        }
        observers.append(minimizeObserver)
        
        // 窗口恢复观察者
        let deminiaturizeObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didDeminiaturizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowStates[identifier] = .normal
        }
        observers.append(deminiaturizeObserver)
        
        // 全屏观察者
        let fullscreenObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didEnterFullScreenNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowStates[identifier] = .fullscreen
        }
        observers.append(fullscreenObserver)
        
        let exitFullscreenObserver = NotificationCenter.default.addObserver(
            forName: NSWindow.didExitFullScreenNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowStates[identifier] = .normal
        }
        observers.append(exitFullscreenObserver)
        
        windowObservers[identifier] = observers
        #endif
    }
    
    private func cleanupWindow(identifier: String) {
        // 移除观察者
        if let observers = windowObservers[identifier] {
            for observer in observers {
                NotificationCenter.default.removeObserver(observer)
            }
            windowObservers.removeValue(forKey: identifier)
        }
        
        // 保存窗口位置
        saveWindowPosition(identifier: identifier)
        
        // 移除窗口引用
        windows.removeValue(forKey: identifier)
        windowStates.removeValue(forKey: identifier)
    }
    
    private func setupApplicationObservers() {
        #if canImport(AppKit)
        // 应用即将终止
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillTerminate),
            name: NSApplication.willTerminateNotification,
            object: nil
        )
        
        // 应用隐藏
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidHide),
            name: NSApplication.didHideNotification,
            object: nil
        )
        
        // 应用显示
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidUnhide),
            name: NSApplication.didUnhideNotification,
            object: nil
        )
        #endif
    }
    
    @objc private func applicationWillTerminate() {
        // 保存所有窗口位置
        for identifier in windows.keys {
            saveWindowPosition(identifier: identifier)
        }
    }
    
    @objc private func applicationDidHide() {
        // 应用隐藏时的处理
    }
    
    @objc private func applicationDidUnhide() {
        // 应用显示时的处理
    }
    
    private func cleanup() {
        for identifier in windows.keys {
            cleanupWindow(identifier: identifier)
        }
    }
}

// MARK: - SwiftUI Integration

extension WindowManager {
    
    /// SwiftUI 视图修饰符，用于管理窗口
    struct WindowManagerModifier: ViewModifier {
        let identifier: String
        let configuration: WindowConfiguration
        
        func body(content: Content) -> some View {
            content
                .onAppear {
                    WindowManager.shared.restoreWindowPosition(identifier: identifier)
                }
                .onDisappear {
                    WindowManager.shared.saveWindowPosition(identifier: identifier)
                }
        }
    }
}

extension View {
    /// 应用窗口管理
    func windowManaged(
        identifier: String,
        configuration: WindowManager.WindowConfiguration = .default
    ) -> some View {
        self.modifier(WindowManager.WindowManagerModifier(
            identifier: identifier,
            configuration: configuration
        ))
    }
}

// MARK: - Window Factory

struct WindowFactory {
    
    /// 创建主窗口
    @MainActor
    static func createMainWindow<Content: View>(
        contentView: Content,
        onClose: (() -> Void)? = nil
    ) -> NSWindow? {
        return WindowManager.shared.createWindow(
            with: .default,
            contentView: contentView,
            onClose: onClose
        )
    }
    
    /// 创建浮动窗口
    @MainActor
    static func createFloatingWindow<Content: View>(
        contentView: Content,
        onClose: (() -> Void)? = nil
    ) -> NSWindow? {
        return WindowManager.shared.createWindow(
            with: .floating,
            contentView: contentView,
            onClose: onClose
        )
    }
    
    /// 创建调试窗口
    @MainActor
    static func createDebugWindow<Content: View>(
        contentView: Content,
        onClose: (() -> Void)? = nil
    ) -> NSWindow? {
        return WindowManager.shared.createWindow(
            with: .debug,
            contentView: contentView,
            onClose: onClose
        )
    }
}