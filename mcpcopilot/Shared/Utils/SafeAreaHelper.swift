//
//  SafeAreaHelper.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI
import AppKit

/// 安全区域和窗口管理辅助工具
struct SafeAreaHelper {
    
    // MARK: - Window Management
    
    /// 获取主屏幕的安全区域
    static var mainScreenSafeArea: NSEdgeInsets {
        guard let screen = NSScreen.main else {
            return NSEdgeInsets()
        }
        
        let frame = screen.frame
        let visibleFrame = screen.visibleFrame
        
        return NSEdgeInsets(
            top: frame.maxY - visibleFrame.maxY,
            left: visibleFrame.minX - frame.minX,
            bottom: visibleFrame.minY - frame.minY,
            right: frame.maxX - visibleFrame.maxX
        )
    }
    
    /// 获取当前窗口的安全区域
    static func safeArea(for window: NSWindow?) -> NSEdgeInsets {
        guard let window = window,
              let screen = window.screen else {
            return mainScreenSafeArea
        }
        
        let windowFrame = window.frame
        let screenVisibleFrame = screen.visibleFrame
        
        return NSEdgeInsets(
            top: max(0, screenVisibleFrame.maxY - windowFrame.maxY),
            left: max(0, windowFrame.minX - screenVisibleFrame.minX),
            bottom: max(0, windowFrame.minY - screenVisibleFrame.minY),
            right: max(0, screenVisibleFrame.maxX - windowFrame.maxX)
        )
    }
    
    /// 计算窗口的最佳位置（居中显示）
    static func centerPosition(for size: CGSize, in screen: NSScreen? = nil) -> CGPoint {
        let targetScreen = screen ?? NSScreen.main ?? NSScreen.screens.first!
        let screenFrame = targetScreen.visibleFrame
        
        let x = screenFrame.midX - size.width / 2
        let y = screenFrame.midY - size.height / 2
        
        return CGPoint(x: x, y: y)
    }
    
    /// 确保窗口在屏幕可见区域内
    static func constrainToScreen(_ frame: CGRect, screen: NSScreen? = nil) -> CGRect {
        let targetScreen = screen ?? NSScreen.main ?? NSScreen.screens.first!
        let screenFrame = targetScreen.visibleFrame
        
        var constrainedFrame = frame
        
        // 确保窗口不超出屏幕右边界
        if constrainedFrame.maxX > screenFrame.maxX {
            constrainedFrame.origin.x = screenFrame.maxX - constrainedFrame.width
        }
        
        // 确保窗口不超出屏幕左边界
        if constrainedFrame.minX < screenFrame.minX {
            constrainedFrame.origin.x = screenFrame.minX
        }
        
        // 确保窗口不超出屏幕上边界
        if constrainedFrame.maxY > screenFrame.maxY {
            constrainedFrame.origin.y = screenFrame.maxY - constrainedFrame.height
        }
        
        // 确保窗口不超出屏幕下边界
        if constrainedFrame.minY < screenFrame.minY {
            constrainedFrame.origin.y = screenFrame.minY
        }
        
        return constrainedFrame
    }
}

// MARK: - SwiftUI Extensions
extension View {
    
    /// 应用安全区域内边距
    func safeAreaPadding(_ edges: Edge.Set = .all) -> some View {
        self.padding(edges, 0) // macOS 通常不需要额外的安全区域内边距
    }
    
    /// 忽略安全区域
    func ignoreSafeArea(_ regions: SafeAreaRegions = .all, edges: Edge.Set = .all) -> some View {
        self.ignoresSafeArea(regions, edges: edges)
    }
}

// MARK: - Window State Management
class WindowStateManager: ObservableObject {
    
    @Published var isFullScreen = false
    @Published var windowFrame: CGRect = .zero
    @Published var isMinimized = false
    
    private var window: NSWindow?
    
    init(window: NSWindow? = nil) {
        self.window = window
        setupObservers()
    }
    
    private func setupObservers() {
        NotificationCenter.default.addObserver(
            forName: NSWindow.didEnterFullScreenNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.isFullScreen = true
        }
        
        NotificationCenter.default.addObserver(
            forName: NSWindow.didExitFullScreenNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.isFullScreen = false
        }
        
        NotificationCenter.default.addObserver(
            forName: NSWindow.didResizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.windowFrame = self?.window?.frame ?? .zero
        }
        
        NotificationCenter.default.addObserver(
            forName: NSWindow.didMiniaturizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.isMinimized = true
        }
        
        NotificationCenter.default.addObserver(
            forName: NSWindow.didDeminiaturizeNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.isMinimized = false
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Screen Utilities
extension NSScreen {
    
    /// 获取屏幕的缩放因子
    var scaleFactor: CGFloat {
        return self.backingScaleFactor
    }
    
    /// 检查是否为 Retina 屏幕
    var isRetina: Bool {
        return scaleFactor > 1.0
    }
    
    /// 获取屏幕的工作区域（排除 Dock 和菜单栏）
    var workingArea: CGRect {
        return visibleFrame
    }
}
