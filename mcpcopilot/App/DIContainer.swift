//
//  DIContainer.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import SwiftUI

/// 依赖注入容器，管理应用中的所有服务实例
@MainActor
class DIContainer: ObservableObject {

    // MARK: - Singleton
    static let shared = DIContainer()

    // MARK: - Services
    private(set) lazy var configurationService: ConfigurationServiceProtocol = ConfigurationService()
    private(set) lazy var processService: ProcessServiceProtocol = ProcessService(loggingService: loggingService)
    private(set) lazy var monitoringService: MonitoringServiceProtocol = MonitoringService(loggingService: loggingService)
    private(set) lazy var loggingService: LoggingServiceProtocol = LoggingService()
    private(set) lazy var storageService: StorageServiceProtocol = StorageService()
    private(set) lazy var localizationService: LocalizationServiceProtocol = LocalizationService()
    private(set) lazy var performanceMonitoringService: PerformanceMonitoringServiceProtocol = PerformanceMonitoringService()
    private(set) lazy var cacheService: CacheServiceProtocol = CacheService()
    private(set) lazy var securityService: SecurityServiceProtocol = SecurityService()
    
    // MARK: - New Services (Rive & Godot)
    private(set) lazy var animationService: AnimationServiceProtocol = AnimationService(loggingService: loggingService)
    private(set) lazy var gameEngineService: GameEngineServiceProtocol = GameEngineService(
        loggingService: loggingService,
        configurationService: configurationService
    )
    
    // MARK: - Managers
    private(set) lazy var windowManager: WindowManager = WindowManager.shared
    private(set) lazy var designTokens: DesignTokens = DesignTokens.shared

    // MARK: - Repositories
    private(set) lazy var sceneRepository: SceneRepositoryProtocol = SceneRepository(
        storageService: storageService,
        configurationService: configurationService
    )

    private(set) lazy var configurationRepository: ConfigurationRepositoryProtocol = ConfigurationRepository(
        storageService: storageService
    )

    // MARK: - ViewModels
    private(set) lazy var homeViewModel: HomeViewModel = HomeViewModel(
        sceneRepository: sceneRepository,
        processService: processService,
        monitoringService: monitoringService
    )

    private(set) lazy var settingsViewModel: SettingsViewModel = SettingsViewModel(
        configurationRepository: configurationRepository,
        storageService: storageService,
        localizationService: localizationService
    )

    private(set) lazy var monitorViewModel: MonitorViewModel = MonitorViewModel(
        monitoringService: monitoringService,
        processService: processService,
        loggingService: loggingService
    )

    private(set) lazy var configurationViewModel: ConfigurationViewModel = ConfigurationViewModel(
        configurationRepository: configurationRepository,
        sceneRepository: sceneRepository,
        securityService: securityService,
        cacheService: cacheService
    )

    private init() {
        setupServices()
    }

    private func setupServices() {
        // 初始化服务配置
        Task {
            await configurationService.initialize()
            await storageService.initialize()
            await loggingService.initialize()
            
            // 初始化新服务
            await animationService.preloadAnimations()
            await gameEngineService.initializeEngine()
        }
    }
}

// MARK: - Environment Key
struct DIContainerKey: EnvironmentKey {
    static let defaultValue: DIContainer = DIContainer.shared
}

extension EnvironmentValues {
    var diContainer: DIContainer {
        get { self[DIContainerKey.self] }
        set { self[DIContainerKey.self] = newValue }
    }
}
