/* 
  Localizable.strings (Chinese Simplified)
  mcpcopilot

  Created by 张国豪 on 2025/7/14.
*/

// MARK: - General
"OK" = "确定";
"Cancel" = "取消";
"Save" = "保存";
"Delete" = "删除";
"Edit" = "编辑";
"Add" = "添加";
"Remove" = "移除";
"Start" = "启动";
"Stop" = "停止";
"Restart" = "重启";
"Loading" = "加载中...";
"Error" = "错误";
"Warning" = "警告";
"Info" = "信息";
"Success" = "成功";

// MARK: - App
"app.title" = "MCP Copilot";
"app.description" = "MCP 本地桥接客户端";

// MARK: - Scene Management
"scene.title" = "场景";
"scene.create" = "创建场景";
"scene.edit" = "编辑场景";
"scene.delete" = "删除场景";
"scene.start" = "启动场景";
"scene.stop" = "停止场景";
"scene.restart" = "重启场景";
"scene.name" = "场景名称";
"scene.port" = "端口";
"scene.status" = "状态";
"scene.servers" = "服务器";
"scene.environment" = "环境变量";
"scene.created" = "创建时间";
"scene.updated" = "更新时间";

// MARK: - Scene Status
"scene.status.running" = "运行中";
"scene.status.stopped" = "已停止";
"scene.status.starting" = "启动中";
"scene.status.stopping" = "停止中";
"scene.status.error" = "错误";

// MARK: - Server Management
"server.title" = "MCP 服务器";
"server.add" = "添加服务器";
"server.edit" = "编辑服务器";
"server.delete" = "删除服务器";
"server.name" = "服务器名称";
"server.namespace" = "命名空间";
"server.type" = "类型";
"server.command" = "命令";
"server.arguments" = "参数";

// MARK: - Environment Variables
"env.title" = "环境变量";
"env.add" = "添加环境变量";
"env.key" = "变量名";
"env.value" = "变量值";
"env.empty" = "暂无环境变量";

// MARK: - Monitoring
"monitor.title" = "监控";
"monitor.cpu" = "CPU 使用率";
"monitor.memory" = "内存使用";
"monitor.uptime" = "运行时间";
"monitor.pid" = "进程 ID";

// MARK: - Settings
"settings.title" = "设置";
"settings.general" = "通用";
"settings.advanced" = "高级";
"settings.about" = "关于";
"settings.default_port" = "默认端口";
"settings.auto_start" = "自动启动";
"settings.log_level" = "日志级别";
"settings.max_restart_attempts" = "最大重启次数";
"settings.monitoring_interval" = "监控间隔";
"settings.system_tray" = "系统托盘";
"settings.language" = "语言";

// MARK: - Logs
"log.title" = "日志";
"log.clear" = "清空日志";
"log.export" = "导出日志";
"log.level.debug" = "调试";
"log.level.info" = "信息";
"log.level.warning" = "警告";
"log.level.error" = "错误";

// MARK: - Menu Bar
"menubar.open_main_window" = "打开主窗口";
"menubar.create_scene" = "创建新场景";
"menubar.stop_all_scenes" = "停止所有场景";
"menubar.settings" = "设置";
"menubar.view_logs" = "查看日志";
"menubar.about" = "关于";
"menubar.quit" = "退出";

// MARK: - Empty States
"empty.scenes" = "暂无场景";
"empty.scenes.description" = "点击上方的 + 按钮创建第一个场景";
"empty.servers" = "暂无 MCP 服务器";
"empty.servers.description" = "点击上方的 + 按钮添加第一个服务器";
"empty.environment" = "暂无环境变量";
"empty.environment.description" = "点击编辑按钮添加环境变量";

// MARK: - Welcome
"welcome.title" = "欢迎使用 MCP Copilot";
"welcome.description" = "选择左侧的场景来查看详细信息，或创建新的场景来开始使用。";
"welcome.create_scene" = "创建场景";
"welcome.view_docs" = "查看文档";

// MARK: - Errors
"error.load_scenes" = "加载场景失败";
"error.save_scene" = "保存场景失败";
"error.delete_scene" = "删除场景失败";
"error.start_scene" = "启动场景失败";
"error.stop_scene" = "停止场景失败";
"error.restart_scene" = "重启场景失败";
"error.invalid_input" = "请检查输入的信息是否正确";
"error.invalid_port" = "端口号必须是有效的数字";

// MARK: - Confirmations
"confirm.delete_scene" = "确定要删除场景 \"%@\" 吗？此操作无法撤销。";
"confirm.stop_all_scenes" = "确定要停止所有运行中的场景吗？";

// MARK: - Tooltips
"tooltip.start_scene" = "启动场景";
"tooltip.stop_scene" = "停止场景";
"tooltip.restart_scene" = "重启场景";
"tooltip.more_options" = "更多选项";

// MARK: - Placeholders
"placeholder.scene_name" = "输入场景名称";
"placeholder.port_number" = "输入端口号";
"placeholder.env_key" = "变量名";
"placeholder.env_value" = "变量值";

// MARK: - Reset
"Reset" = "重置";
