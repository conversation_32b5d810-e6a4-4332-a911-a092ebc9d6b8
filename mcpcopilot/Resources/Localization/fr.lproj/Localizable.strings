/* 
  Localizable.strings (French)
  mcpcopilot

  Created by 张国豪 on 2025/7/15.
*/

// MARK: - General
"OK" = "OK";
"Cancel" = "Annuler";
"Save" = "Enregistrer";
"Delete" = "Supprimer";
"Edit" = "Modifier";
"Add" = "Ajouter";
"Remove" = "Supprimer";
"Start" = "Démarrer";
"Stop" = "Arrêter";
"Restart" = "Redémarrer";
"Loading" = "Chargement...";
"Error" = "Erreur";
"Warning" = "Avertissement";
"Info" = "Info";
"Success" = "Succès";

// MARK: - App
"app.title" = "MCP Copilot";
"app.description" = "Client Bridge Local MCP";

// MARK: - Scene Management
"scene.title" = "Scènes";
"scene.create" = "Créer une scène";
"scene.edit" = "Modifier la scène";
"scene.delete" = "Supprimer la scène";
"scene.start" = "Démarrer la scène";
"scene.stop" = "Arrêter la scène";
"scene.restart" = "Redémarrer la scène";
"scene.name" = "Nom de la scène";
"scene.port" = "Port";
"scene.status" = "Statut";
"scene.servers" = "Serveurs";
"scene.environment" = "Variables d'environnement";
"scene.created" = "Créé";
"scene.updated" = "Mis à jour";

// MARK: - Scene Status
"scene.status.running" = "En cours";
"scene.status.stopped" = "Arrêté";
"scene.status.starting" = "Démarrage";
"scene.status.stopping" = "Arrêt";
"scene.status.error" = "Erreur";

// MARK: - Server Management
"server.title" = "Serveurs MCP";
"server.add" = "Ajouter un serveur";
"server.edit" = "Modifier le serveur";
"server.delete" = "Supprimer le serveur";
"server.name" = "Nom du serveur";
"server.namespace" = "Espace de noms";
"server.type" = "Type";
"server.command" = "Commande";
"server.arguments" = "Arguments";

// MARK: - Environment Variables
"env.title" = "Variables d'environnement";
"env.add" = "Ajouter une variable d'environnement";
"env.key" = "Clé";
"env.value" = "Valeur";
"env.empty" = "Aucune variable d'environnement";

// MARK: - Monitoring
"monitor.title" = "Surveillance";
"monitor.cpu" = "Utilisation CPU";
"monitor.memory" = "Utilisation mémoire";
"monitor.uptime" = "Temps de fonctionnement";
"monitor.pid" = "ID du processus";

// MARK: - Settings
"settings.title" = "Paramètres";
"settings.general" = "Général";
"settings.advanced" = "Avancé";
"settings.about" = "À propos";
"settings.default_port" = "Port par défaut";
"settings.auto_start" = "Démarrage automatique";
"settings.log_level" = "Niveau de journal";
"settings.max_restart_attempts" = "Tentatives de redémarrage max";
"settings.monitoring_interval" = "Intervalle de surveillance";
"settings.system_tray" = "Barre système";
"settings.language" = "Langue";

// MARK: - Logs
"log.title" = "Journaux";
"log.clear" = "Effacer les journaux";
"log.export" = "Exporter les journaux";
"log.level.debug" = "Débogage";
"log.level.info" = "Info";
"log.level.warning" = "Avertissement";
"log.level.error" = "Erreur";

// MARK: - Menu Bar
"menubar.open_main_window" = "Ouvrir la fenêtre principale";
"menubar.create_scene" = "Créer une nouvelle scène";
"menubar.stop_all_scenes" = "Arrêter toutes les scènes";
"menubar.settings" = "Paramètres";
"menubar.view_logs" = "Afficher les journaux";
"menubar.about" = "À propos";
"menubar.quit" = "Quitter";

// MARK: - Empty States
"empty.scenes" = "Aucune scène";
"empty.scenes.description" = "Cliquez sur le bouton + ci-dessus pour créer votre première scène";
"empty.servers" = "Aucun serveur MCP";
"empty.servers.description" = "Cliquez sur le bouton + ci-dessus pour ajouter votre premier serveur";
"empty.environment" = "Aucune variable d'environnement";
"empty.environment.description" = "Cliquez sur le bouton modifier pour ajouter des variables d'environnement";

// MARK: - Welcome
"welcome.title" = "Bienvenue dans MCP Copilot";
"welcome.description" = "Sélectionnez une scène dans la barre latérale pour afficher les détails, ou créez une nouvelle scène pour commencer.";
"welcome.create_scene" = "Créer une scène";
"welcome.view_docs" = "Afficher la documentation";

// MARK: - Errors
"error.load_scenes" = "Échec du chargement des scènes";
"error.save_scene" = "Échec de l'enregistrement de la scène";
"error.delete_scene" = "Échec de la suppression de la scène";
"error.start_scene" = "Échec du démarrage de la scène";
"error.stop_scene" = "Échec de l'arrêt de la scène";
"error.restart_scene" = "Échec du redémarrage de la scène";
"error.invalid_input" = "Veuillez vérifier si les informations saisies sont correctes";
"error.invalid_port" = "Le numéro de port doit être un nombre valide";

// MARK: - Confirmations
"confirm.delete_scene" = "Êtes-vous sûr de vouloir supprimer la scène \"%@\" ? Cette action ne peut pas être annulée.";
"confirm.stop_all_scenes" = "Êtes-vous sûr de vouloir arrêter toutes les scènes en cours ?";

// MARK: - Tooltips
"tooltip.start_scene" = "Démarrer la scène";
"tooltip.stop_scene" = "Arrêter la scène";
"tooltip.restart_scene" = "Redémarrer la scène";
"tooltip.more_options" = "Plus d'options";

// MARK: - Placeholders
"placeholder.scene_name" = "Entrez le nom de la scène";
"placeholder.port_number" = "Entrez le numéro de port";
"placeholder.env_key" = "Clé";
"placeholder.env_value" = "Valeur";

// MARK: - Reset
"Reset" = "Réinitialiser";