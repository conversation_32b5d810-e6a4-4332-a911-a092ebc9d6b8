/* 
  Localizable.strings (Japanese)
  mcpcopilot

  Created by 张国豪 on 2025/7/15.
*/

// MARK: - General
"OK" = "OK";
"Cancel" = "キャンセル";
"Save" = "保存";
"Delete" = "削除";
"Edit" = "編集";
"Add" = "追加";
"Remove" = "削除";
"Start" = "開始";
"Stop" = "停止";
"Restart" = "再起動";
"Loading" = "読み込み中...";
"Error" = "エラー";
"Warning" = "警告";
"Info" = "情報";
"Success" = "成功";

// MARK: - App
"app.title" = "MCP Copilot";
"app.description" = "MCP ローカルブリッジクライアント";

// MARK: - Scene Management
"scene.title" = "シーン";
"scene.create" = "シーンを作成";
"scene.edit" = "シーンを編集";
"scene.delete" = "シーンを削除";
"scene.start" = "シーンを開始";
"scene.stop" = "シーンを停止";
"scene.restart" = "シーンを再起動";
"scene.name" = "シーン名";
"scene.port" = "ポート";
"scene.status" = "ステータス";
"scene.servers" = "サーバー";
"scene.environment" = "環境変数";
"scene.created" = "作成日";
"scene.updated" = "更新日";

// MARK: - Scene Status
"scene.status.running" = "実行中";
"scene.status.stopped" = "停止中";
"scene.status.starting" = "開始中";
"scene.status.stopping" = "停止中";
"scene.status.error" = "エラー";

// MARK: - Server Management
"server.title" = "MCPサーバー";
"server.add" = "サーバーを追加";
"server.edit" = "サーバーを編集";
"server.delete" = "サーバーを削除";
"server.name" = "サーバー名";
"server.namespace" = "ネームスペース";
"server.type" = "タイプ";
"server.command" = "コマンド";
"server.arguments" = "引数";

// MARK: - Environment Variables
"env.title" = "環境変数";
"env.add" = "環境変数を追加";
"env.key" = "キー";
"env.value" = "値";
"env.empty" = "環境変数なし";

// MARK: - Monitoring
"monitor.title" = "モニタリング";
"monitor.cpu" = "CPU使用率";
"monitor.memory" = "メモリ使用量";
"monitor.uptime" = "稼働時間";
"monitor.pid" = "プロセスID";

// MARK: - Settings
"settings.title" = "設定";
"settings.general" = "一般";
"settings.advanced" = "詳細";
"settings.about" = "について";
"settings.default_port" = "デフォルトポート";
"settings.auto_start" = "自動開始";
"settings.log_level" = "ログレベル";
"settings.max_restart_attempts" = "最大再起動試行回数";
"settings.monitoring_interval" = "モニタリング間隔";
"settings.system_tray" = "システムトレイ";
"settings.language" = "言語";

// MARK: - Logs
"log.title" = "ログ";
"log.clear" = "ログをクリア";
"log.export" = "ログをエクスポート";
"log.level.debug" = "デバッグ";
"log.level.info" = "情報";
"log.level.warning" = "警告";
"log.level.error" = "エラー";

// MARK: - Menu Bar
"menubar.open_main_window" = "メインウィンドウを開く";
"menubar.create_scene" = "新しいシーンを作成";
"menubar.stop_all_scenes" = "すべてのシーンを停止";
"menubar.settings" = "設定";
"menubar.view_logs" = "ログを表示";
"menubar.about" = "について";
"menubar.quit" = "終了";

// MARK: - Empty States
"empty.scenes" = "シーンなし";
"empty.scenes.description" = "上の+ボタンをクリックして最初のシーンを作成してください";
"empty.servers" = "MCPサーバーなし";
"empty.servers.description" = "上の+ボタンをクリックして最初のサーバーを追加してください";
"empty.environment" = "環境変数なし";
"empty.environment.description" = "編集ボタンをクリックして環境変数を追加してください";

// MARK: - Welcome
"welcome.title" = "MCP Copilotへようこそ";
"welcome.description" = "サイドバーからシーンを選択して詳細を表示するか、新しいシーンを作成して開始してください。";
"welcome.create_scene" = "シーンを作成";
"welcome.view_docs" = "ドキュメントを表示";

// MARK: - Errors
"error.load_scenes" = "シーンの読み込みに失敗しました";
"error.save_scene" = "シーンの保存に失敗しました";
"error.delete_scene" = "シーンの削除に失敗しました";
"error.start_scene" = "シーンの開始に失敗しました";
"error.stop_scene" = "シーンの停止に失敗しました";
"error.restart_scene" = "シーンの再起動に失敗しました";
"error.invalid_input" = "入力情報が正しいかどうかを確認してください";
"error.invalid_port" = "ポート番号は有効な数値である必要があります";

// MARK: - Confirmations
"confirm.delete_scene" = "シーン \"%@\" を削除してもよろしいですか？この操作は元に戻せません。";
"confirm.stop_all_scenes" = "実行中のすべてのシーンを停止してもよろしいですか？";

// MARK: - Tooltips
"tooltip.start_scene" = "シーンを開始";
"tooltip.stop_scene" = "シーンを停止";
"tooltip.restart_scene" = "シーンを再起動";
"tooltip.more_options" = "その他のオプション";

// MARK: - Placeholders
"placeholder.scene_name" = "シーン名を入力";
"placeholder.port_number" = "ポート番号を入力";
"placeholder.env_key" = "キー";
"placeholder.env_value" = "値";

// MARK: - Reset
"Reset" = "リセット";