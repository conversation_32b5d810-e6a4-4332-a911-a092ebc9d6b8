/* 
  Localizable.strings (German)
  mcpcopilot

  Created by 张国豪 on 2025/7/15.
*/

// MARK: - General
"OK" = "OK";
"Cancel" = "Abbrechen";
"Save" = "Speichern";
"Delete" = "Löschen";
"Edit" = "Bearbeiten";
"Add" = "Hinzufügen";
"Remove" = "Entfernen";
"Start" = "Starten";
"Stop" = "Stoppen";
"Restart" = "Neustarten";
"Loading" = "Wird geladen...";
"Error" = "Fehler";
"Warning" = "Warnung";
"Info" = "Info";
"Success" = "Erfolg";

// MARK: - App
"app.title" = "MCP Copilot";
"app.description" = "MCP Local Bridge Client";

// MARK: - Scene Management
"scene.title" = "Szenen";
"scene.create" = "Szene erstellen";
"scene.edit" = "Szene bearbeiten";
"scene.delete" = "Szene löschen";
"scene.start" = "Szene starten";
"scene.stop" = "Szene stoppen";
"scene.restart" = "Szene neustarten";
"scene.name" = "Szenenname";
"scene.port" = "Port";
"scene.status" = "Status";
"scene.servers" = "Server";
"scene.environment" = "Umgebungsvariablen";
"scene.created" = "Erstellt";
"scene.updated" = "Aktualisiert";

// MARK: - Scene Status
"scene.status.running" = "Läuft";
"scene.status.stopped" = "Gestoppt";
"scene.status.starting" = "Startet";
"scene.status.stopping" = "Stoppt";
"scene.status.error" = "Fehler";

// MARK: - Server Management
"server.title" = "MCP Server";
"server.add" = "Server hinzufügen";
"server.edit" = "Server bearbeiten";
"server.delete" = "Server löschen";
"server.name" = "Servername";
"server.namespace" = "Namespace";
"server.type" = "Typ";
"server.command" = "Befehl";
"server.arguments" = "Argumente";

// MARK: - Environment Variables
"env.title" = "Umgebungsvariablen";
"env.add" = "Umgebungsvariable hinzufügen";
"env.key" = "Schlüssel";
"env.value" = "Wert";
"env.empty" = "Keine Umgebungsvariablen";

// MARK: - Monitoring
"monitor.title" = "Überwachung";
"monitor.cpu" = "CPU-Auslastung";
"monitor.memory" = "Speicherverbrauch";
"monitor.uptime" = "Betriebszeit";
"monitor.pid" = "Prozess-ID";

// MARK: - Settings
"settings.title" = "Einstellungen";
"settings.general" = "Allgemein";
"settings.advanced" = "Erweitert";
"settings.about" = "Über";
"settings.default_port" = "Standard-Port";
"settings.auto_start" = "Automatischer Start";
"settings.log_level" = "Log-Level";
"settings.max_restart_attempts" = "Max. Neustartversuche";
"settings.monitoring_interval" = "Überwachungsintervall";
"settings.system_tray" = "System-Tray";
"settings.language" = "Sprache";

// MARK: - Logs
"log.title" = "Logs";
"log.clear" = "Logs löschen";
"log.export" = "Logs exportieren";
"log.level.debug" = "Debug";
"log.level.info" = "Info";
"log.level.warning" = "Warnung";
"log.level.error" = "Fehler";

// MARK: - Menu Bar
"menubar.open_main_window" = "Hauptfenster öffnen";
"menubar.create_scene" = "Neue Szene erstellen";
"menubar.stop_all_scenes" = "Alle Szenen stoppen";
"menubar.settings" = "Einstellungen";
"menubar.view_logs" = "Logs anzeigen";
"menubar.about" = "Über";
"menubar.quit" = "Beenden";

// MARK: - Empty States
"empty.scenes" = "Keine Szenen";
"empty.scenes.description" = "Klicken Sie auf die + Schaltfläche oben, um Ihre erste Szene zu erstellen";
"empty.servers" = "Keine MCP-Server";
"empty.servers.description" = "Klicken Sie auf die + Schaltfläche oben, um Ihren ersten Server hinzuzufügen";
"empty.environment" = "Keine Umgebungsvariablen";
"empty.environment.description" = "Klicken Sie auf die Bearbeiten-Schaltfläche, um Umgebungsvariablen hinzuzufügen";

// MARK: - Welcome
"welcome.title" = "Willkommen bei MCP Copilot";
"welcome.description" = "Wählen Sie eine Szene aus der Seitenleiste aus, um Details anzuzeigen, oder erstellen Sie eine neue Szene, um zu beginnen.";
"welcome.create_scene" = "Szene erstellen";
"welcome.view_docs" = "Dokumentation anzeigen";

// MARK: - Errors
"error.load_scenes" = "Szenen konnten nicht geladen werden";
"error.save_scene" = "Szene konnte nicht gespeichert werden";
"error.delete_scene" = "Szene konnte nicht gelöscht werden";
"error.start_scene" = "Szene konnte nicht gestartet werden";
"error.stop_scene" = "Szene konnte nicht gestoppt werden";
"error.restart_scene" = "Szene konnte nicht neu gestartet werden";
"error.invalid_input" = "Bitte überprüfen Sie, ob die Eingabeinformationen korrekt sind";
"error.invalid_port" = "Port-Nummer muss eine gültige Zahl sein";

// MARK: - Confirmations
"confirm.delete_scene" = "Sind Sie sicher, dass Sie die Szene \"%@\" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"confirm.stop_all_scenes" = "Sind Sie sicher, dass Sie alle laufenden Szenen stoppen möchten?";

// MARK: - Tooltips
"tooltip.start_scene" = "Szene starten";
"tooltip.stop_scene" = "Szene stoppen";
"tooltip.restart_scene" = "Szene neu starten";
"tooltip.more_options" = "Weitere Optionen";

// MARK: - Placeholders
"placeholder.scene_name" = "Szenenname eingeben";
"placeholder.port_number" = "Port-Nummer eingeben";
"placeholder.env_key" = "Schlüssel";
"placeholder.env_value" = "Wert";

// MARK: - Reset
"Reset" = "Zurücksetzen";