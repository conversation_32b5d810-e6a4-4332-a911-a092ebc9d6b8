# MCP 本地桥接客户端（macOS 原生版）产品设计文档

## 一、产品定位

一个基于 `yamcp` 和 `supergateway` 构建的 **macOS 原生桌面客户端**，用于管理多个 MCP Server 场景，并统一通过 HTTP/SSE 接口暴露给本地 AI 客户端（如 Cursor、Claude Desktop、Cline、OpenCat 等）。支持 GUI 场景配置、运行状态管理、资源监控、客户端配置写入，目标是打造一个轻量级、开箱即用的 MCP 网关控制中心。

## 二、目标用户与使用场景

| 角色        | 使用场景                            | 价值说明            |
| --------- | ------------------------------- | --------------- |
| AI 开发者    | 同时运行多个模型服务（如 DeepSeek、Claude 等） | 集中管理，统一接入，性能可观测 |
| 运维 / 工程人员 | 为不同任务（代码生成、监控分析）切换工作区           | 场景隔离，启动/关闭一键切换  |
| 团队成员共享配置  | 导入他人导出的 MCP 配置，快速复用             | 配置即代码，支持版本控制    |

## 三、核心功能

### 1. 场景管理

* 添加/删除/编辑 MCP 场景（workspace）
* 每个场景包含若干 MCP Server
* 每个场景配置监听端口（默认从 7700 开始）

### 2. MCP Server 配置管理

* 基于标准 MCP Provider JSON 格式
* 支持命令/参数编辑、类型选择（stdio/http）
* 可视化添加 server 到不同场景中

### 3. 启动控制与进程管理

* 启动/停止某个场景
* 启动时自动执行：

  * `yamcp server import`
  * `yamcp run <scene>`
  * `supergateway --stdio "yamcp run <scene>"`
* 管理子进程生命周期与重启策略

### 4. 资源监控

* 实时显示每个 MCP Server 的 CPU 使用率 / 内存 / 状态
* 展示启动时间、PID、重启次数
* 异常崩溃自动重启（配置最大次数）

### 5. 客户端配置写入

* 一键写入配置至：

  * `~/.cursor/mcp.json`
  * Claude Desktop config 文件
  * 复制 Endpoint URL

### 6. 系统级集成

* 托盘菜单：切换场景、快速重启、退出
* 启动项管理（LaunchAgent）
* 日志查看与导出

## 四、架构设计

```
┌────────────────────────┐
│ SwiftUI 主界面         │
└────────┬───────────────┘
         │
┌────────▼────────────┐
│ Swift 后端控制器     │── spawn yamcp / supergateway 子进程
└────────┬────────────┘
         │ stdio 监听
┌────────▼────────────┐
│ yamcp run <scene>   │
└────────┬────────────┘
         │
┌────────▼────────────┐
│ supergateway        │──▶ 暴露 StreamableHTTP 接口
└─────────────────────┘
```

## 五、配置结构

```json
{
  "scenes": [
    {
      "name": "dev",
      "port": 7701,
      "servers": ["deepseek", "seq-thinking"],
      "env": { "OPENAI_API_KEY": "sk-xxx" }
    }
  ],
  "mcpServers": {
    "deepseek": {
      "namespace": "deepseek",
      "type": "stdio",
      "providerParameters": {
        "command": "deepseek-llm",
        "args": ["--port", "9000"]
      }
    }
  }
}
```

## 六、关键技术实现

| 功能模块   | 技术选型                                         |
| ------ | -------------------------------------------- |
| GUI 前端 | SwiftUI + Combine / SwiftData                |
| 子进程控制  | `Process` / `Pipe` / `DispatchIO`            |
| 文件操作   | `FileManager`, `Codable`, `UserDefaults`     |
| 资源监控   | `sysctl`, `ActivityMonitor`, `sysinfo` crate |
| 日志管理   | `Pipe` + 文件尾部读取                              |
| 托盘菜单   | `NSStatusBar`, `MenuBarExtra`, AppKit bridge |
| 自启动    | 写入 `~/Library/LaunchAgents/*.plist`          |

## 七、后续扩展方向

* 插件市场：集成 MCP Provider 市场索引，支持远程一键添加
* 多设备同步：配置通过 Git 或 iCloud 同步
* API 密钥管理：Keychain 集成加密存储
* 云端发布：ngrok / frp 支持公网代理

## 八、开发计划（建议）

| 阶段 | 目标        | 内容                                | 周期  |
| -- | --------- | --------------------------------- | --- |
| P0 | MVP 初版    | 启停场景、显示 Server 状态、暴露端口            | 1 周 |
| P1 | 配置可视化管理   | 增加配置编辑界面、热重载                      | 1 周 |
| P2 | 资源监控 + 日志 | 展示 CPU/RSS、子进程 stdout 日志          | 1 周 |
| P3 | 客户端配置写入   | 支持 Cursor / Claude / OpenCat 一键接入 | 1 周 |
| P4 | 系统集成优化    | 托盘、开机启动、窗口优化                      | 1 周 |

---

如需产品截图、流程图、接口草图、代码骨架，可后续添加。
