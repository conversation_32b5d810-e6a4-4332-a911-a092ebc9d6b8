# MCP Copilot 项目结构

本项目已按照标准苹果生态项目结构进行重构，采用分层设计、模块化架构和现代 SwiftUI 开发模式。

## 📁 项目结构

```
mcpcopilot/
├── App/                           # 应用层
│   ├── DIContainer.swift          # 依赖注入容器
│   └── MCPCopilotApp.swift        # 主应用入口
├── Data/                          # 数据层
│   ├── Models/
│   │   └── DataModels.swift       # 数据模型定义
│   └── Repository/
│       ├── SceneRepository.swift  # 场景数据仓库
│       └── ConfigurationRepository.swift # 配置数据仓库
├── Modules/                       # 功能模块层
│   ├── Home/                      # 主页模块
│   │   ├── View/
│   │   │   └── HomeView.swift
│   │   └── ViewModel/
│   │       └── HomeViewModel.swift
│   ├── Settings/                  # 设置模块
│   │   ├── View/
│   │   │   └── SettingsView.swift
│   │   └── ViewModel/
│   │       └── SettingsViewModel.swift
│   ├── Monitor/                   # 监控模块
│   │   ├── View/
│   │   │   └── MonitorView.swift
│   │   └── ViewModel/
│   │       └── MonitorViewModel.swift
│   └── Configuration/             # 配置模块（待实现）
├── Resources/                     # 资源文件
│   ├── Assets.xcassets/           # 图片资源
│   └── Localization/              # 国际化资源
│       ├── en.lproj/
│       │   └── Localizable.strings
│       └── zh-Hans.lproj/
│           └── Localizable.strings
└── Shared/                        # 共享层
    ├── Components/                # 共享组件
    │   ├── SceneRowView.swift
    │   ├── SceneDetailView.swift
    │   ├── SceneEditView.swift
    │   └── MenuBarView.swift
    ├── Resources/                 # 设计系统
    │   ├── Colors.swift           # 颜色系统
    │   ├── Spacing.swift          # 间距系统
    │   └── Typography.swift       # 字体系统
    ├── Services/                  # 服务层
    │   ├── Configuration/
    │   │   └── ConfigurationService.swift
    │   ├── Process/
    │   │   └── ProcessService.swift
    │   ├── Monitoring/
    │   │   └── MonitoringService.swift
    │   ├── Logging/
    │   │   └── LoggingService.swift
    │   ├── Storage/
    │   │   └── StorageService.swift
    │   └── ServiceProtocols.swift
    └── Utils/                     # 工具类（待实现）
```

## 🏗️ 架构设计

### 1. 分层架构

- **App层**: 应用启动和依赖注入管理
- **Data层**: 数据模型和数据访问逻辑
- **Modules层**: 按功能组织的业务模块
- **Shared层**: 跨模块共享的组件和服务

### 2. 依赖注入

- 使用 `DIContainer` 管理所有服务和 ViewModel 实例
- 通过 SwiftUI Environment 传递依赖
- 支持单例模式和生命周期管理

### 3. MVVM 模式

- 每个功能模块包含 View 和 ViewModel
- ViewModel 负责业务逻辑和状态管理
- View 专注于 UI 展示和用户交互

### 4. 服务层设计

- 基于协议的服务接口设计
- 支持异步操作和错误处理
- 服务间解耦，便于测试和维护

## 🎨 设计系统

### 颜色系统 (Colors.swift)

- 主色调：蓝色、绿色、红色、橙色、紫色
- 语义化颜色：成功、警告、错误、信息
- 支持深色模式适配
- 状态相关颜色：进程状态、日志级别

### 间距系统 (Spacing.swift)

- 基础间距单位：xs(4), sm(8), md(16), lg(24), xl(32)
- 组件特定间距：卡片、按钮、表单、列表等
- 响应式间距支持

### 字体系统 (Typography.swift)

- 系统字体和自定义字体
- 语义化字体：标题、正文、标签、代码等
- 支持不同字重和设计风格

## 🔧 核心功能

### 1. 场景管理

- 创建、编辑、删除 MCP 场景
- 场景启动、停止、重启控制
- 环境变量配置

### 2. 进程管理

- yamcp 和 supergateway 进程控制
- 进程状态监控和自动重启
- 进程输出日志收集

### 3. 系统监控

- 实时 CPU 和内存使用率监控
- 进程资源使用情况跟踪
- 可视化监控界面

### 4. 日志系统

- 多级别日志记录
- 日志过滤和搜索
- 日志导出功能

### 5. 配置管理

- 应用配置持久化
- 用户偏好设置
- 配置导入导出

## 🌐 国际化支持

- 支持中文简体和英文
- 完整的本地化字符串
- 可扩展的多语言架构

## 📱 用户界面

### 主界面 (HomeView)

- 侧边栏场景列表
- 场景详情展示
- 快速操作按钮

### 设置界面 (SettingsView)

- 通用设置：端口、自动启动、语言等
- 高级设置：日志级别、监控间隔等
- 关于信息

### 监控界面 (MonitorView)

- 系统资源监控图表
- 进程列表和状态
- 实时日志查看

### 菜单栏 (MenuBarView)

- 快速场景切换
- 系统托盘集成
- 常用操作快捷方式

## 🔧 新增功能特性

### 1. 高级服务层

- **性能监控服务**: 实时 CPU、内存、磁盘使用率监控
- **缓存服务**: 智能缓存管理，支持过期策略和自动清理
- **安全服务**: Keychain 集成、数据加密、API Key 管理
- **本地化服务**: 多语言支持，动态语言切换

### 2. 调试和开发工具

- **调试浮动面板**: 实时性能指标显示，快速操作面板
- **性能监控视图**: 详细的性能分析和历史数据
- **本地化演示**: 实时测试本地化功能

### 3. 配置管理增强

- **配置导入导出**: 支持单个场景或全部配置的导入导出
- **配置验证**: 自动检测配置冲突和问题
- **配置复制**: 快速复制场景配置

### 4. 用户体验优化

- **浮动窗口管理**: 智能窗口定位和拖拽支持
- **安全区域适配**: macOS 特定的窗口管理
- **响应式设计**: 适配不同屏幕尺寸

## 🎨 设计系统完善

### 新增组件

- **FloatingButton**: 浮动操作按钮
- **DebugFloatingPanel**: 调试面板组件
- **MetricCard**: 性能指标卡片
- **ValidationIssueRow**: 配置验证问题行

### 工具类扩展

- **SafeAreaHelper**: 安全区域和窗口管理
- **FloatingPositionHelper**: 浮动元素定位
- **WindowStateManager**: 窗口状态管理

## 🔐 安全性增强

### 数据保护

- **Keychain 集成**: 敏感数据安全存储
- **数据加密**: AES-GCM 加密算法
- **API Key 验证**: 智能 API Key 格式验证
- **环境变量安全**: 防止敏感信息泄露

### 完整性验证

- **数据哈希**: SHA-256 数据完整性验证
- **安全比较**: 防时序攻击的字符串比较
- **配置验证**: 自动检测配置问题

## 🌐 国际化完善

### 多语言支持

- **动态语言切换**: 无需重启应用
- **完整本地化**: 所有 UI 文本本地化
- **参数化字符串**: 支持动态参数插入
- **语言检测**: 自动检测系统语言

### 本地化工具

- **实时测试**: 本地化键值实时测试
- **语言状态**: 当前语言状态显示
- **本地化扩展**: 便捷的本地化方法

## 📊 性能监控系统

### 实时监控

- **系统资源**: CPU、内存、磁盘使用率
- **进程监控**: 单个进程资源使用
- **自定义指标**: 支持自定义性能指标
- **执行时间**: 操作执行时间测量

### 警告系统

- **阈值监控**: 可配置的警告阈值
- **内存压力**: 系统内存压力检测
- **性能警告**: 自动性能警告通知

## 🚀 下一步开发

1. **完善进程管理**: 实现 yamcp 和 supergateway 的实际集成
2. **监控图表**: 添加实时监控图表和历史数据
3. **插件系统**: 支持 MCP Server 插件市场
4. **云端同步**: 配置的云端备份和同步
5. **性能优化**: 内存使用优化和启动速度提升
6. **自动化测试**: 完善单元测试和集成测试

## 🧪 测试建议

1. **单元测试**: 为 ViewModel 和 Service 编写单元测试
2. **集成测试**: 测试服务间的交互和数据流
3. **UI测试**: 使用 SwiftUI Preview 和 UI 测试框架
4. **性能测试**: 监控内存使用和 CPU 性能
5. **安全测试**: 验证数据加密和安全存储
6. **本地化测试**: 测试多语言支持和动态切换

## 📈 项目优势

### 架构优势

- **模块化设计**: 高内聚低耦合的模块结构
- **依赖注入**: 便于测试和维护的依赖管理
- **协议导向**: 基于协议的服务接口设计
- **异步支持**: 全面的 async/await 支持

### 开发体验

- **调试工具**: 内置调试面板和性能监控
- **热重载**: SwiftUI Preview 支持
- **类型安全**: 强类型系统和编译时检查
- **代码复用**: 高度可复用的组件和服务

### 用户体验

- **响应式界面**: 流畅的用户交互
- **多语言支持**: 完整的国际化体验
- **性能监控**: 实时系统状态显示
- **配置管理**: 便捷的配置导入导出

这个重构后的项目结构提供了企业级的可维护性、可扩展性和代码组织，符合现代 macOS 应用开发的最佳实践，并为未来的功能扩展奠定了坚实的基础。
